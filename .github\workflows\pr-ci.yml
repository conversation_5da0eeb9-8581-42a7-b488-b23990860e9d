name: Pull Request CI

on:
  workflow_dispatch:
  pull_request:
    branches:
      - main
      - develop

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install corepack
        run: corepack enable && corepack prepare yarn@4.6.0 --activate

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - name: Cache yarn dependencies
        uses: actions/cache@v4
        with:
          path: |
            ${{ steps.yarn-cache-dir-path.outputs.dir }}
            node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install Dependencies
        run: yarn install

      - name: Build Check
        run: yarn build:check

      - name: Lint Check
        run: yarn lint
