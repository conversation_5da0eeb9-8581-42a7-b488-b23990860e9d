<h1 align="center">
  <a href="https://github.com/CherryHQ/cherry-studio/releases">
    <img src="https://github.com/CherryHQ/cherry-studio/blob/main/build/icon.png?raw=true" width="150" height="150" alt="banner" /><br>
  </a>
</h1>
<p align="center">
  <a href="https://github.com/CherryHQ/cherry-studio">English</a> | <a href="./README.zh.md">中文</a> | 日本語 | <a href="https://cherry-ai.com">公式サイト</a> | <a href="https://docs.cherry-ai.com/cherry-studio-wen-dang/ja">ドキュメント</a> | <a href="./dev.md">開発</a> | <a href="https://github.com/CherryHQ/cherry-studio/issues">フィードバック</a><br>
</p>

<!-- バッジコレクション -->

<div align="center">

[![][deepwiki-shield]][deepwiki-link]
[![][twitter-shield]][twitter-link]
[![][discord-shield]][discord-link]
[![][telegram-shield]][telegram-link]

</div>

<!-- プロジェクト統計 -->

<div align="center">

[![][github-stars-shield]][github-stars-link]
[![][github-forks-shield]][github-forks-link]
[![][github-release-shield]][github-release-link]
[![][github-contributors-shield]][github-contributors-link]

</div>

<div align="center">

[![][license-shield]][license-link]
[![][commercial-shield]][commercial-link]
[![][sponsor-shield]][sponsor-link]

</div>

<div align="center">
 <a href="https://hellogithub.com/repository/1605492e1e2a4df3be07abfa4578dd37" target="_blank"><img src="https://api.hellogithub.com/v1/widgets/recommend.svg?rid=1605492e1e2a4df3be07abfa4578dd37" alt="Featured｜HelloGitHub" style="width: 200px; height: 43px;" width="200" height="43" /></a>
 <a href="https://trendshift.io/repositories/11772" target="_blank"><img src="https://trendshift.io/api/badge/repositories/11772" alt="kangfenmao%2Fcherry-studio | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>
 <a href="https://www.producthunt.com/posts/cherry-studio?embed=true&utm_source=badge-featured&utm_medium=badge&utm_souce=badge-cherry&#0045;studio" target="_blank"><img src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=496640&theme=light" alt="Cherry&#0032;Studio - AI&#0032;Chatbots&#0044;&#0032;AI&#0032;Desktop&#0032;Client | Product Hunt" style="width: 200px; height: 43px;" width="200" height="43" /></a>
</div>

# 🍒 Cherry Studio

Cherry Studio は、複数の LLM プロバイダーをサポートするデスクトップクライアントで、Windows、Mac、Linux で利用可能です。

👏 [Telegram](https://t.me/CherryStudioAI)｜[Discord](https://discord.gg/wez8HtpxqQ) | [QQグループ(575014769)](https://qm.qq.com/q/lo0D4qVZKi)

❤️ Cherry Studio をお気に入りにしましたか？小さな星をつけてください 🌟 または [スポンサー](sponsor.md) をして開発をサポートしてください！

# 🌠 スクリーンショット

![](https://github.com/user-attachments/assets/36dddb2c-e0fb-4a5f-9411-91447bab6e18)

![](https://github.com/user-attachments/assets/f549e8a0-2385-40b4-b52b-2039e39f2930)

![](https://github.com/user-attachments/assets/58e0237c-4d36-40de-b428-53051d982026)

# 🌟 主な機能

1. **多様な LLM サービス対応**：

- ☁️ 主要な LLM クラウドサービス対応：OpenAI、Gemini、Anthropic など
- 🔗 AI Web サービス統合：Claude、Peplexity、Poe など
- 💻 Ollama、LM Studio によるローカルモデル実行対応

2. **AI アシスタントと対話**：

- 📚 300+ の事前設定済み AI アシスタント
- 🤖 カスタム AI アシスタントの作成
- 💬 複数モデルでの同時対話機能

3. **文書とデータ処理**：

- 📄 テキスト、画像、Office、PDF など多様な形式対応
- ☁️ WebDAV によるファイル管理とバックアップ
- 📊 Mermaid による図表作成
- 💻 コードハイライト機能

4. **実用的なツール統合**：

- 🔍 グローバル検索機能
- 📝 トピック管理システム
- 🔤 AI による翻訳機能
- 🎯 ドラッグ＆ドロップによる整理
- 🔌 ミニプログラム対応
- ⚙️ MCP（モデルコンテキストプロトコル）サービス

5. **優れたユーザー体験**：

- 🖥️ Windows、Mac、Linux のクロスプラットフォーム対応
- 📦 環境構築不要ですぐに使用可能
- 🎨 ライト/ダークテーマと透明ウィンドウ対応
- 📝 完全な Markdown レンダリング
- 🤲 簡単な共有機能

# 📝 開発計画

以下の機能と改善に積極的に取り組んでいます：

1. 🎯 **コア機能**

- 選択アシスタント - スマートな内容選択の強化
- ディープリサーチ - 高度な研究能力
- メモリーシステム - グローバルコンテキスト認識
- ドキュメント前処理 - 文書処理の改善
- MCP マーケットプレイス - モデルコンテキストプロトコルエコシステム

2. 🗂 **ナレッジ管理**

- ノートとコレクション
- ダイナミックキャンバス可視化
- OCR 機能
- TTS（テキスト読み上げ）サポート

3. 📱 **プラットフォーム対応**

- HarmonyOS エディション
- Android アプリ（フェーズ1）
- iOS アプリ（フェーズ1）
- マルチウィンドウ対応
- ウィンドウピン留め機能

4. 🔌 **高度な機能**

- プラグインシステム
- ASR（音声認識）
- アシスタントとトピックの対話機能リファクタリング

[プロジェクトボード](https://github.com/orgs/CherryHQ/projects/7)で進捗を確認し、貢献することができます。

開発計画に影響を与えたいですか？[GitHub ディスカッション](https://github.com/CherryHQ/cherry-studio/discussions)に参加して、アイデアやフィードバックを共有してください！

# 🌈 テーマ

- テーマギャラリー：https://cherrycss.com
- Aero テーマ：https://github.com/hakadao/CherryStudio-Aero
- PaperMaterial テーマ：https://github.com/rainoffallingstar/CherryStudio-PaperMaterial
- Claude テーマ：https://github.com/bjl101501/CherryStudio-Claudestyle-dynamic
- メープルネオンテーマ：https://github.com/BoningtonChen/CherryStudio_themes

より多くのテーマの PR を歓迎します

# 🤝 貢献

Cherry Studio への貢献を歓迎します！以下の方法で貢献できます：

1. **コードの貢献**：新機能を開発するか、既存のコードを最適化します
2. **バグの修正**：見つけたバグを修正します
3. **問題の管理**：GitHub の問題を管理するのを手伝います
4. **製品デザイン**：デザインの議論に参加します
5. **ドキュメントの作成**：ユーザーマニュアルやガイドを改善します
6. **コミュニティの参加**：ディスカッションに参加し、ユーザーを支援します
7. **使用の促進**：Cherry Studio を広めます

[ブランチ戦略](branching-strategy-en.md)を参照して貢献ガイドラインを確認してください

## 始め方

1. **リポジトリをフォーク**：フォークしてローカルマシンにクローンします
2. **ブランチを作成**：変更のためのブランチを作成します
3. **変更を提出**：変更をコミットしてプッシュします
4. **プルリクエストを開く**：変更内容と理由を説明します

詳細なガイドラインについては、[貢献ガイド](../CONTRIBUTING.md)をご覧ください。

ご支援と貢献に感謝します！

# 🔗 関連プロジェクト

- [one-api](https://github.com/songquanpeng/one-api)：LLM API の管理・配信システム。OpenAI、Azure、Anthropic などの主要モデルに対応し、統一 API インターフェースを提供。API キー管理と再配布に利用可能。

- [ublacklist](https://github.com/iorate/ublacklist)：Google 検索結果から特定のサイトを非表示にします

# 🚀 コントリビューター

<a href="https://github.com/CherryHQ/cherry-studio/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=CherryHQ/cherry-studio" />
</a>
<br /><br />

# ⭐️ スター履歴

[![Star History Chart](https://api.star-history.com/svg?repos=CherryHQ/cherry-studio&type=Timeline)](https://star-history.com/#CherryHQ/cherry-studio&Timeline)

<!-- リンクと画像 -->
[deepwiki-shield]: https://img.shields.io/badge/Deepwiki-CherryHQ-0088CC?style=plastic
[deepwiki-link]: https://deepwiki.com/CherryHQ/cherry-studio
[twitter-shield]: https://img.shields.io/badge/Twitter-CherryStudioApp-0088CC?style=plastic&logo=x
[twitter-link]: https://twitter.com/CherryStudioHQ
[discord-shield]: https://img.shields.io/badge/Discord-@CherryStudio-0088CC?style=plastic&logo=discord
[discord-link]: https://discord.gg/wez8HtpxqQ
[telegram-shield]: https://img.shields.io/badge/Telegram-@CherryStudioAI-0088CC?style=plastic&logo=telegram
[telegram-link]: https://t.me/CherryStudioAI

<!-- プロジェクト統計 -->
[github-stars-shield]: https://img.shields.io/github/stars/CherryHQ/cherry-studio?style=social
[github-stars-link]: https://github.com/CherryHQ/cherry-studio/stargazers
[github-forks-shield]: https://img.shields.io/github/forks/CherryHQ/cherry-studio?style=social
[github-forks-link]: https://github.com/CherryHQ/cherry-studio/network
[github-release-shield]: https://img.shields.io/github/v/release/CherryHQ/cherry-studio
[github-release-link]: https://github.com/CherryHQ/cherry-studio/releases
[github-contributors-shield]: https://img.shields.io/github/contributors/CherryHQ/cherry-studio
[github-contributors-link]: https://github.com/CherryHQ/cherry-studio/graphs/contributors

<!-- ライセンスとスポンサー -->
[license-shield]: https://img.shields.io/badge/License-AGPLv3-important.svg?style=plastic&logo=gnu
[license-link]: https://www.gnu.org/licenses/agpl-3.0
[commercial-shield]: https://img.shields.io/badge/商用ライセンス-お問い合わせ-white.svg?style=plastic&logoColor=white&logo=telegram&color=blue
[commercial-link]: mailto:<EMAIL>?subject=商業ライセンスについて
[sponsor-shield]: https://img.shields.io/badge/スポンサー-FF6699.svg?style=plastic&logo=githubsponsors&logoColor=white
[sponsor-link]: https://github.com/CherryHQ/cherry-studio/blob/main/docs/sponsor.md
