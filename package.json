{"name": "QDminQ", "version": "1.4.3", "private": true, "description": "青岛勘测专用的AI助手", "main": "./out/main/index.js", "author": "青岛勘测院", "homepage": "https://github.com/CherryHQ/cherry-studio", "workspaces": {"packages": ["local", "packages/*"], "installConfig": {"hoistingLimits": ["packages/database"]}}, "scripts": {"start": "electron-vite preview", "dev": "electron-vite dev", "debug": "electron-vite -- --inspect --sourcemap --remote-debugging-port=9222", "build": "npm run typecheck && electron-vite build", "build:check": "yarn typecheck && yarn check:i18n && yarn test", "build:unpack": "dotenv npm run build && electron-builder --dir", "build:win": "dotenv npm run build && electron-builder --win --x64 --arm64", "build:win:x64": "dotenv npm run build && electron-builder --win --x64", "build:win:arm64": "dotenv npm run build && electron-builder --win --arm64", "build:mac": "dotenv electron-vite build && electron-builder --mac --arm64 --x64", "build:mac:arm64": "dotenv electron-vite build && electron-builder --mac --arm64", "build:mac:x64": "dotenv electron-vite build && electron-builder --mac --x64", "build:linux": "dotenv electron-vite build && electron-builder --linux --x64 --arm64", "build:linux:arm64": "dotenv electron-vite build && electron-builder --linux --arm64", "build:linux:x64": "dotenv electron-vite build && electron-builder --linux --x64", "build:npm": "node scripts/build-npm.js", "release": "node scripts/version.js", "publish": "yarn build:check && yarn release patch push", "pulish:artifacts": "cd packages/artifacts && npm publish && cd -", "generate:agents": "yarn workspace @cherry-studio/database agents", "analyze:renderer": "VISUALIZER_RENDERER=true yarn build", "analyze:main": "VISUALIZER_MAIN=true yarn build", "typecheck": "npm run typecheck:node && npm run typecheck:web", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "check:i18n": "node scripts/check-i18n.js", "test": "vitest run --silent", "test:main": "vitest run --project main", "test:renderer": "vitest run --project renderer", "test:update": "yarn test:renderer --update", "test:coverage": "vitest run --coverage --silent", "test:ui": "vitest --ui", "test:watch": "vitest", "test:e2e": "yarn playwright test", "test:lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "prepare": "husky"}, "dependencies": {"@cherrystudio/embedjs": "^0.1.31", "@cherrystudio/embedjs-libsql": "^0.1.31", "@cherrystudio/embedjs-loader-csv": "^0.1.31", "@cherrystudio/embedjs-loader-image": "^0.1.31", "@cherrystudio/embedjs-loader-markdown": "^0.1.31", "@cherrystudio/embedjs-loader-msoffice": "^0.1.31", "@cherrystudio/embedjs-loader-pdf": "^0.1.31", "@cherrystudio/embedjs-loader-sitemap": "^0.1.31", "@cherrystudio/embedjs-loader-web": "^0.1.31", "@cherrystudio/embedjs-loader-xml": "^0.1.31", "@cherrystudio/embedjs-ollama": "^0.1.31", "@cherrystudio/embedjs-openai": "^0.1.31", "@electron-toolkit/utils": "^3.0.0", "@langchain/community": "^0.3.36", "@langchain/ollama": "^0.2.1", "@strongtz/win32-arm64-msvc": "^0.4.7", "@tanstack/react-query": "^5.27.0", "@types/react-infinite-scroll-component": "^5.0.0", "archiver": "^7.0.1", "async-mutex": "^0.5.0", "diff": "^7.0.0", "docx": "^9.0.2", "electron-log": "^5.1.5", "electron-store": "^8.2.0", "electron-updater": "6.6.4", "electron-window-state": "^5.0.3", "epub": "patch:epub@npm%3A1.3.0#~/.yarn/patches/epub-npm-1.3.0-8325494ffe.patch", "fast-xml-parser": "^5.2.0", "franc-min": "^6.2.0", "fs-extra": "^11.2.0", "jsdom": "^26.0.0", "markdown-it": "^14.1.0", "node-stream-zip": "^1.15.0", "officeparser": "^4.1.1", "os-proxy-config": "^1.1.2", "proxy-agent": "^6.5.0", "remove-markdown": "^0.6.2", "selection-hook": "^0.9.23", "tar": "^7.4.3", "turndown": "^7.2.0", "webdav": "^5.8.0", "zipread": "^1.3.3"}, "devDependencies": {"@agentic/exa": "^7.3.3", "@agentic/searxng": "^7.3.3", "@agentic/tavily": "^7.3.3", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@anthropic-ai/sdk": "^0.41.0", "@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@electron/notarize": "^2.5.0", "@emotion/is-prop-valid": "^1.3.1", "@eslint-react/eslint-plugin": "^1.36.1", "@eslint/js": "^9.22.0", "@google/genai": "^1.0.1", "@hello-pangea/dnd": "^16.6.0", "@kangfenmao/keyv-storage": "^0.1.0", "@modelcontextprotocol/sdk": "^1.11.4", "@mozilla/readability": "^0.6.0", "@notionhq/client": "^2.2.15", "@playwright/test": "^1.52.0", "@reduxjs/toolkit": "^2.2.5", "@shikijs/markdown-it": "^3.4.2", "@swc/plugin-styled-components": "^7.1.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@tryfabric/martian": "^1.2.4", "@types/diff": "^7", "@types/fs-extra": "^11", "@types/lodash": "^4.17.5", "@types/markdown-it": "^14", "@types/md5": "^2.3.5", "@types/node": "^18.19.9", "@types/pako": "^1.0.2", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/react-infinite-scroll-component": "^5.0.0", "@types/react-window": "^1", "@types/tinycolor2": "^1", "@uiw/codemirror-extensions-langs": "^4.23.12", "@uiw/codemirror-themes-all": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/browser": "^3.1.4", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "@vitest/web-worker": "^3.1.4", "@xyflow/react": "^12.4.4", "antd": "^5.22.5", "axios": "^1.7.3", "browser-image-compression": "^2.0.2", "color": "^5.0.0", "dayjs": "^1.11.11", "dexie": "^4.0.8", "dexie-react-hooks": "^1.1.7", "dotenv-cli": "^7.4.2", "electron": "35.4.0", "electron-builder": "26.0.15", "electron-devtools-installer": "^3.2.0", "electron-vite": "^3.1.0", "emittery": "^1.0.3", "emoji-picker-element": "^1.22.1", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "fast-diff": "^1.3.0", "html-to-image": "^1.11.13", "husky": "^9.1.7", "i18next": "^23.11.5", "jest-styled-components": "^7.2.0", "lint-staged": "^15.5.0", "lodash": "^4.17.21", "lru-cache": "^11.1.0", "lucide-react": "^0.487.0", "mermaid": "^11.6.0", "mime": "^4.0.4", "motion": "^12.10.5", "npx-scope-finder": "^1.2.0", "openai": "patch:openai@npm%3A5.1.0#~/.yarn/patches/openai-npm-5.1.0-0e7b3ccb07.patch", "p-queue": "^8.1.0", "playwright": "^1.52.0", "prettier": "^3.5.3", "rc-virtual-list": "^3.18.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hotkeys-hook": "^4.6.1", "react-i18next": "^14.1.2", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-redux": "^9.1.2", "react-router": "6", "react-router-dom": "6", "react-spinners": "^0.14.1", "react-window": "^1.8.11", "redux": "^5.0.1", "redux-persist": "^6.0.0", "rehype-katex": "^7.0.1", "rehype-mathjax": "^7.0.0", "rehype-raw": "^7.0.0", "remark-cjk-friendly": "^1.1.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.88.0", "shiki": "^3.4.2", "string-width": "^7.2.0", "styled-components": "^6.1.11", "tiny-pinyin": "^1.3.2", "tokenx": "^0.4.1", "typescript": "^5.6.2", "uuid": "^10.0.0", "vite": "6.2.6", "vitest": "^3.1.4"}, "resolutions": {"pdf-parse@npm:1.1.1": "patch:pdf-parse@npm%3A1.1.1#~/.yarn/patches/pdf-parse-npm-1.1.1-04a6109b2a.patch", "@langchain/openai@npm:^0.3.16": "patch:@langchain/openai@npm%3A0.3.16#~/.yarn/patches/@langchain-openai-npm-0.3.16-e525b59526.patch", "@langchain/openai@npm:>=0.1.0 <0.4.0": "patch:@langchain/openai@npm%3A0.3.16#~/.yarn/patches/@langchain-openai-npm-0.3.16-e525b59526.patch", "libsql@npm:^0.4.4": "patch:libsql@npm%3A0.4.7#~/.yarn/patches/libsql-npm-0.4.7-444e260fb1.patch", "openai@npm:^4.77.0": "patch:openai@npm%3A5.1.0#~/.yarn/patches/openai-npm-5.1.0-0e7b3ccb07.patch", "pkce-challenge@npm:^4.1.0": "patch:pkce-challenge@npm%3A4.1.0#~/.yarn/patches/pkce-challenge-npm-4.1.0-fbc51695a3.patch", "app-builder-lib@npm:26.0.13": "patch:app-builder-lib@npm%3A26.0.13#~/.yarn/patches/app-builder-lib-npm-26.0.13-a064c9e1d0.patch", "openai@npm:^4.87.3": "patch:openai@npm%3A5.1.0#~/.yarn/patches/openai-npm-5.1.0-0e7b3ccb07.patch", "app-builder-lib@npm:26.0.15": "patch:app-builder-lib@npm%3A26.0.15#~/.yarn/patches/app-builder-lib-npm-26.0.15-360e5b0476.patch", "@langchain/core@npm:^0.3.26": "patch:@langchain/core@npm%3A0.3.44#~/.yarn/patches/@langchain-core-npm-0.3.44-41d5c3cb0a.patch"}, "packageManager": "yarn@4.9.1", "lint-staged": {"*.{js,jsx,ts,tsx,cjs,mjs,cts,mts}": ["prettier --write", "eslint --fix"], "*.{json,md,yml,yaml,css,scss,html}": ["prettier --write"]}}