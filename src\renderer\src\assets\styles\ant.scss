@use './container.scss';

#inputbar {
  resize: none;
}

.ant-image-preview-switch-left {
  -webkit-app-region: no-drag;
}

.ant-btn:not(:disabled):focus-visible {
  outline: none;
}

.ant-tabs-tabpane:focus-visible {
  outline: none;
}

.ant-tabs-tab-btn {
  outline: none !important;
}

.ant-segmented-group {
  gap: 4px;
}

.minapp-drawer {
  max-width: calc(100vw - var(--sidebar-width));
  .ant-drawer-content-wrapper {
    box-shadow: none;
  }
  .ant-drawer-header {
    position: absolute;
    -webkit-app-region: drag;
    min-height: calc(var(--navbar-height) + 0.5px);
    width: calc(100vw - var(--sidebar-width));
    margin-top: -0.5px;
    border-bottom: none;
  }
  .ant-drawer-body {
    padding: 0;
    margin-top: var(--navbar-height);
    overflow: hidden;
    @extend #content-container;
  }
  .minapp-mask {
    background-color: transparent !important;
  }
}

.ant-drawer-header {
  -webkit-app-region: no-drag;
}

.message-attachments {
  .ant-upload-list-item:hover {
    background-color: initial !important;
  }
}

.mention-models-dropdown {
  &.ant-dropdown {
    background: rgba(var(--color-base-rgb), 0.65) !important;
    backdrop-filter: blur(35px) saturate(150%) !important;
    animation-duration: 0.15s !important;
  }

  /* 移动其他样式到 mention-models-dropdown 类下 */
  .ant-slide-up-enter .ant-dropdown-menu,
  .ant-slide-up-appear .ant-dropdown-menu,
  .ant-slide-up-leave .ant-dropdown-menu,
  .ant-slide-up-enter-active .ant-dropdown-menu,
  .ant-slide-up-appear-active .ant-dropdown-menu,
  .ant-slide-up-leave-active .ant-dropdown-menu {
    background: rgba(var(--color-base-rgb), 0.65) !important;
    backdrop-filter: blur(35px) saturate(150%) !important;
  }

  .ant-dropdown-menu {
    /* 保持原有的下拉菜单样式，但限定在 mention-models-dropdown 类下 */
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 4px 12px;
    position: relative;
    background: rgba(var(--color-base-rgb), 0.65) !important;
    backdrop-filter: blur(35px) saturate(150%) !important;
    border: 0.5px solid rgba(var(--color-border-rgb), 0.3);
    border-radius: 10px;
    box-shadow:
      0 0 0 0.5px rgba(0, 0, 0, 0.15),
      0 4px 16px rgba(0, 0, 0, 0.15),
      0 2px 8px rgba(0, 0, 0, 0.12),
      inset 0 0 0 0.5px rgba(255, 255, 255, var(--inner-glow-opacity, 0.1));
    transform-origin: top;
    will-change: transform, opacity;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 0;

    &.no-scrollbar {
      padding-right: 12px;
    }

    &.has-scrollbar {
      padding-right: 2px;
    }

    // Scrollbar styles
    &::-webkit-scrollbar {
      width: 14px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border: 4px solid transparent;
      background-clip: padding-box;
      border-radius: 7px;
      background-color: var(--color-scrollbar-thumb);
      min-height: 50px;
      transition: all 0.2s;
    }

    &:hover::-webkit-scrollbar-thumb {
      background-color: var(--color-scrollbar-thumb);
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: var(--color-scrollbar-thumb-hover);
    }

    &::-webkit-scrollbar-thumb:active {
      background-color: var(--color-scrollbar-thumb-hover);
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 7px;
    }
  }

  .ant-dropdown-menu-item-group {
    margin-bottom: 4px;

    &:not(:first-child) {
      margin-top: 4px;
    }

    .ant-dropdown-menu-item-group-title {
      padding: 5px 12px;
      color: var(--color-text-3);
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.03em;
      opacity: 0.7;
    }
  }

  // Handle no-results case margin
  .no-results {
    padding: 8px 12px;
    color: var(--color-text-3);
    cursor: default;
    font-size: 13px;
    opacity: 0.8;
    margin-bottom: 40px;

    &:hover {
      background: none;
    }
  }

  .ant-dropdown-menu-item {
    padding: 5px 12px;
    margin: 0 -12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 6px;
    font-size: 13px;

    &:hover {
      background: rgba(var(--color-hover-rgb), 0.5);
    }

    &.ant-dropdown-menu-item-selected {
      background-color: rgba(var(--color-primary-rgb), 0.12);
      color: var(--color-primary);
    }

    .ant-dropdown-menu-item-icon {
      margin-right: 0;
      opacity: 0.9;
    }
  }
}

.ant-dropdown-menu .ant-dropdown-menu-sub {
  max-height: 350px;
  width: max-content;
  overflow-y: auto;
  overflow-x: hidden;
}

.ant-collapse {
  border: 1px solid var(--color-border);
  .ant-color-picker & {
    border: none;
  }
}

.ant-collapse-content {
  border-top: 1px solid var(--color-border) !important;
  .ant-color-picker & {
    border-top: none !important;
  }
}
