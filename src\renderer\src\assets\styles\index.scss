@use './color.scss';
@use './font.scss';
@use './markdown.scss';
@use './ant.scss';
@use './scrollbar.scss';
@use './container.scss';
@use './animation.scss';
@import '../fonts/icon-fonts/iconfont.css';
@import '../fonts/ubuntu/ubuntu.css';

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

*:focus {
  outline: none;
}

* {
  -webkit-tap-highlight-color: transparent;
}

html,
body,
#root {
  height: 100%;
  width: 100%;
  margin: 0;
}

#root {
  display: flex;
  flex-direction: row;
  flex: 1;
}

body {
  display: flex;
  min-height: 100vh;
  color: var(--color-text);
  font-size: 14px;
  line-height: 1.6;
  overflow: hidden;
  font-family: var(--font-family);
  text-rendering: optimizeLegibility;
  transition: background-color 0.3s linear;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

input,
textarea,
[contenteditable='true'],
.markdown,
#messages,
.selectable,
pre,
code {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

a {
  -webkit-user-drag: none;
}

ul {
  list-style: none;
}

.loader {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #000;
  box-shadow:
    32px 0 #000,
    -32px 0 #000;
  position: relative;
  animation: flash 0.5s ease-out infinite alternate;
}

.drag {
  -webkit-app-region: drag;
}

.nodrag {
  -webkit-app-region: no-drag;
}

.text-nowrap {
  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-wrap: break-word;
}

.bubble {
  background-color: var(--chat-background);
  #chat-main {
    background-color: var(--chat-background);
  }
  #messages {
    background-color: var(--chat-background);
  }
  #inputbar {
    margin: -5px 15px 15px 15px;
    background: var(--color-background);
  }
  .system-prompt {
    background-color: var(--chat-background-assistant);
  }
  .message-content-container {
    margin: 5px 0;
    border-radius: 8px;
    padding: 0.5rem 1rem;
  }

  .block-wrapper {
    display: flow-root;
  }

  .block-wrapper:last-child > *:last-child {
    margin-bottom: 0;
  }

  .message-content-container > *:last-child {
    margin-bottom: 0;
  }

  .message-thought-container {
    margin-top: 8px;
  }

  .message-user {
    color: var(--chat-text-user);
    .message-content-container-user .anticon {
      color: var(--chat-text-user) !important;
    }

    .markdown {
      color: var(--chat-text-user);
    }
  }
  .group-grid-container.horizontal,
  .group-grid-container.grid {
    .message-content-container-assistant {
      padding: 0;
    }
  }
  .group-message-wrapper {
    background-color: var(--color-background);
    .message-content-container {
      width: 100%;
    }
  }
  .group-menu-bar {
    background-color: var(--color-background);
  }
  code {
    color: var(--color-text);
  }
  .markdown {
    display: flow-root;
    *:last-child {
      margin-bottom: 0;
    }
  }
}

.lucide {
  color: var(--color-icon);
}

span.highlight {
  background-color: var(--color-background-highlight);
  color: var(--color-highlight);
}

span.highlight.selected {
  background-color: var(--color-background-highlight-accent);
}
