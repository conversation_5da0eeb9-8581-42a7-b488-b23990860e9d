{"translation": {"agents": {"add.button": "Добавить в ассистента", "add.knowledge_base": "База знаний", "add.knowledge_base.placeholder": "Выберите базу знаний", "add.name": "Имя", "add.name.placeholder": "Введите имя", "add.prompt": "Промпт", "add.prompt.placeholder": "Введите промпт", "add.prompt.variables.tip": {"title": "Доступные переменные", "content": "{{date}}:\t<PERSON><PERSON><PERSON><PERSON>\n{{time}}:\t<PERSON><PERSON><PERSON><PERSON><PERSON>\n{{datetime}}:\tДа<PERSON><PERSON> и время\n{{system}}:\tОперационная система\n{{arch}}:\tАрхитектура процессора\n{{language}}:\tЯз<PERSON><PERSON>\n{{model_name}}:\tНазвание модели\n{{username}}:\tИмя пользователя"}, "add.title": "Создать агента", "delete.popup.content": "Вы уверены, что хотите удалить этого агента?", "edit.model.select.title": "Выбрать модель", "edit.title": "Редактировать агента", "manage.title": "Редактировать агентов", "my_agents": "Мои агенты", "search.no_results": "Результаты не найдены", "sorting.title": "Сортировка", "tag.agent": "Агент", "tag.default": "По умолчанию", "tag.new": "Новый", "tag.system": "Система", "title": "Агенты", "import": {"title": "Импорт из внешнего источника", "type": {"url": "URL", "file": "<PERSON>а<PERSON><PERSON>"}, "url_placeholder": "Введите URL JSON", "select_file": "Выбрать файл", "button": "Импорт", "file_filter": "JSON файлы", "error": {"url_required": "Пожалуйста, введите URL", "fetch_failed": "Не удалось получить данные по URL", "invalid_format": "Неверный формат агента: отсутствуют обязательные поля"}}, "export": {"agent": "Экспорт агента"}, "settings": {"title": "Настройки агента"}}, "assistants": {"title": "Ассистенты", "abbr": "Ассистент", "settings.title": "Настройки ассистента", "clear.content": "Очистка топика удалит все топики и файлы в ассистенте. Вы уверены, что хотите продолжить?", "clear.title": "Очистить топики", "copy.title": "Копировать ассистента", "delete.content": "Удаление ассистента удалит все топики и файлы под ассистентом. Вы уверены, что хотите удалить его?", "delete.title": "Удалить ассистента", "edit.title": "Редактировать ассистента", "save.success": "Успешно сохранено", "save.title": "Сохранить в агента", "icon.type": "Иконка ассистента", "search": "Поиск ассистентов...", "settings.mcp": "Серверы MCP", "settings.mcp.enableFirst": "Сначала включите этот сервер в настройках MCP", "settings.mcp.title": "Настройки MCP", "settings.mcp.noServersAvailable": "Нет доступных серверов MCP. Добавьте серверы в настройках", "settings.mcp.description": "Серверы MCP, включенные по умолчанию", "settings.default_model": "Модель по умолчанию", "settings.knowledge_base": "Настройки базы знаний", "settings.model": "Настройки модели", "settings.prompt": "Настройки промптов", "settings.reasoning_effort.off": "Выключить", "settings.reasoning_effort.high": "Стараюсь думать", "settings.reasoning_effort.low": "Меньше думать", "settings.reasoning_effort.medium": "Среднее", "settings.reasoning_effort.default": "По умолчанию", "settings.more": "Настройки ассистента", "settings.reasoning_effort": "Настройки размышлений", "settings.knowledge_base.recognition.tip": "Ассистент будет использовать возможности большой модели для распознавания намерений, чтобы определить, нужно ли обращаться к базе знаний для ответа. Эта функция будет зависеть от возможностей модели", "settings.knowledge_base.recognition": "Использование базы знаний", "settings.knowledge_base.recognition.off": "Принудительный поиск", "settings.knowledge_base.recognition.on": "Распознавание намерений", "settings.tool_use_mode": "Режим использования инструментов", "settings.tool_use_mode.function": "Функция", "settings.tool_use_mode.prompt": "Подсказка", "settings.regular_phrases": {"title": "Регулярные подсказки", "add": "Добавить подсказку", "edit": "Редактировать подсказку", "delete": "Удалить подсказку", "deleteConfirm": "Вы уверены, что хотите удалить эту подсказку?", "titleLabel": "Заголовок", "titlePlaceholder": "Введите заголовок", "contentLabel": "Содержание", "contentPlaceholder": "Введите содержание фразы, поддерживает использование переменных, и нажмите Tab для быстрого перехода к переменной для изменения. Например: \nПомоги мне спланировать маршрут от ${from} до ${to} и отправить его на ${email}."}, "list": {"showByList": "Список", "showByTags": "По тегам"}, "tags": {"untagged": "Несгруппированные метки", "none": "Нет тегов", "manage": "Управление тегами", "add": "Добавить тег", "modify": "Изменить тег", "delete": "Удалить тег", "deleteConfirm": "Вы уверены, что хотите удалить этот тег?", "settings": {"title": "Настройки тегов"}}}, "auth": {"error": "Автоматический получение ключа API не удалось, пожалуйста, получите ключ вручную", "get_key": "Получить", "get_key_success": "Автоматический получение ключа API успешно", "login": "Войти", "oauth_button": "Авторизоваться с {{provider}}"}, "backup": {"confirm": "Вы уверены, что хотите создать резервную копию?", "confirm.button": "Выбрать папку для резервной копии", "content": "Резервная копия будет содержать все данные приложения, включая чаты, настройки и базу знаний. Это может занять некоторое время.", "progress": {"completed": "Резервная копия создана", "compressing": "Сжатие файлов...", "copying_files": "Копирование файлов... {{progress}}%", "preparing": "Подготовка резервной копии...", "title": "Прогресс резервного копирования", "writing_data": "Запись данных..."}, "title": "Резервное копирование данных"}, "button": {"add": "Добавить", "added": "Добавлено", "collapse": "Свернуть", "manage": "Редактировать", "select_model": "Выбрать модель", "show.all": "Показать все", "update_available": "Доступно обновление", "includes_user_questions": "Включает вопросы пользователей", "case_sensitive": "Чувствительность к регистру", "whole_word": "Полное слово"}, "chat": {"add.assistant.title": "Добавить ассистента", "artifacts.button.download": "Скачать", "artifacts.button.openExternal": "Открыть во внешнем браузере", "artifacts.button.preview": "Предпросмотр", "artifacts.preview.openExternal.error.content": "Внешний браузер открылся с ошибкой", "assistant.search.placeholder": "Поиск", "deeply_thought": "Мыслим ({{seconds}} секунд)", "default.description": "Привет, я Ассистент по умолчанию. Вы можете начать общаться со мной прямо сейчас", "default.name": "Ассистент по умолчанию", "default.topic.name": "Топик по умолчанию", "history": {"assistant_node": "Ассистент", "click_to_navigate": "Перейти к сообщению", "coming_soon": "График работы чата скоро появится", "no_messages": "Сообщения не найдены", "start_conversation": "Начните диалог, чтобы просмотреть график работы чата", "title": "История чата", "user_node": "Пользователь", "view_full_content": "Показать полное содержимое"}, "input.auto_resize": "Автоматическая высота", "input.clear": "Очистить {{Command}}", "input.clear.content": "Хотите очистить все сообщения текущего топика?", "input.clear.title": "Очистить все сообщения?", "input.collapse": "Свернуть", "input.context_count.tip": "Контекст / Макс. контекст", "input.estimated_tokens.tip": "Затраты токенов", "input.expand": "Развернуть", "input.file_not_supported": "Модель не поддерживает этот тип файла", "input.file_error": "Ошибка обработки файла", "input.generate_image": "Сгенерировать изображение", "input.generate_image_not_supported": "Модель не поддерживает генерацию изображений.", "input.knowledge_base": "База знаний", "input.new.context": "Очистить контекст {{Command}}", "input.new_topic": "Новый топик {{Command}}", "input.pause": "Остановить", "input.placeholder": "Введите ваше сообщение здесь...", "input.send": "Отправить", "input.settings": "Настройки", "input.topics": " Топики ", "input.translate": "Перевести на {{target_language}}", "input.upload": "Загрузить изображение или документ", "input.upload.document": "Загрузить документ (модель не поддерживает изображения)", "input.web_search": "Веб-поиск", "input.web_search.settings": "Настройки веб-поиска", "input.web_search.button.ok": "Перейти в Настройки", "input.web_search.enable": "Включить веб-поиск", "input.web_search.enable_content": "Необходимо предварительно проверить подключение к веб-поиску в настройках", "message.new.branch": "Новая ветка", "message.new.branch.created": "Новая ветка создана", "message.new.context": "Новый контекст", "message.quote": "Цитата", "message.regenerate.model": "Переключить модель", "message.useful": "Полезно", "multiple.select": "Множественный выбор", "multiple.select.empty": "Ничего не выбрано", "navigation": {"first": "Уже первое сообщение", "history": "История чата", "last": "Уже последнее сообщение", "next": "Следующее сообщение", "prev": "Предыдущее сообщение", "top": "Вернуться наверх", "bottom": "Вернуться вниз", "close": "Закрыть"}, "resend": "Переотправить", "save": "Сохранить", "settings.code.title": "Настройки кода", "settings.code_editor": {"title": "Редактор кода", "highlight_active_line": "Выделить активную строку", "fold_gutter": "Свернуть", "autocompletion": "Автодополнение", "keymap": "Клавиатурные сокращения"}, "settings.code_execution": {"title": "Выполнение кода", "tip": "Выполнение кода в блоке кода возможно, но не рекомендуется выполнять опасный код!", "timeout_minutes": "Время выполнения", "timeout_minutes.tip": "Время выполнения кода (минуты)"}, "settings.code_collapsible": "Блок кода свернут", "settings.code_wrappable": "Блок кода можно переносить", "settings.code_cacheable": "Кэш блока кода", "settings.code_cacheable.tip": "Кэширование блока кода может уменьшить время рендеринга длинных блоков кода, но увеличит использование памяти", "settings.code_cache_max_size": "Максимальный размер кэша", "settings.code_cache_max_size.tip": "Максимальное количество символов, которое может быть кэшировано (тысяч символов), рассчитывается по кэшированному коду. Длина кэшированного кода значительно превышает длину чистого текста.", "settings.code_cache_ttl": "Время жизни кэша", "settings.code_cache_ttl.tip": "Время жизни кэша (минуты)", "settings.code_cache_threshold": "Пороговое значение кэша", "settings.code_cache_threshold.tip": "Минимальное количество символов для кэширования (тысяч символов), рассчитывается по фактическому коду. Будут кэшированы только те блоки кода, которые превышают пороговое значение", "settings.context_count": "Контекст", "settings.context_count.tip": "Количество предыдущих сообщений, которые нужно сохранить в контексте.", "settings.max": "Максимум", "settings.max_tokens": "Максимальное количество токенов", "settings.max_tokens.confirm": "Максимальное количество токенов", "settings.max_tokens.confirm_content": "Установить максимальное количество токенов, влияет на длину результата. Нужно учитывать контекст модели, иначе будет ошибка", "settings.max_tokens.tip": "Максимальное количество токенов, которые может сгенерировать модель. Нужно учитывать контекст модели, иначе будет ошибка", "settings.reset": "Сбросить", "settings.set_as_default": "Применить к ассистенту по умолчанию", "settings.show_line_numbers": "Показать номера строк в коде", "settings.temperature": "Температура", "settings.temperature.tip": "Меньшие значения делают модель более креативной и непредсказуемой, в то время как большие значения делают её более детерминированной и точной.", "settings.thought_auto_collapse": "Автоматически сворачивать содержание мыслей", "settings.thought_auto_collapse.tip": "Автоматически сворачивать содержание мыслей после завершения размышления", "settings.top_p": "Top-P", "settings.top_p.tip": "Значение по умолчанию 1, чем меньше значение, тем меньше вариативности в ответах, тем проще понять, чем больше значение, тем больше вариативности в ответах, тем больше разнообразие", "suggestions.title": "Предложенные вопросы", "thinking": "Мыслим ({{seconds}} секунд)", "topics.auto_rename": "Автопереименование", "topics.clear.title": "Очистить сообщения", "topics.copy.image": "Скопировать как изображение", "topics.copy.md": "Скопировать как Markdown", "topics.copy.plain_text": "Копировать как обычный текст (удалить Markdown)", "topics.copy.title": "Скопировать", "topics.delete.shortcut": "Удерживайте {{key}} для мгновенного удаления", "topics.edit.placeholder": "Введите новый заголовок", "topics.edit.title": "Редактировать заголовок", "topics.export.image": "Экспорт как изображение", "topics.export.joplin": "Экспорт в Joplin", "topics.export.md": "Экспорт как markdown", "topics.export.md.reason": "Экспорт в Markdown (с рассуждениями)", "topics.export.notion": "Экспорт в Notion", "topics.export.obsidian": "Экспорт в Obsidian", "topics.export.obsidian_vault": "Храни<PERSON><PERSON><PERSON>е", "topics.export.obsidian_vault_placeholder": "Выберите имя хранилища", "topics.export.obsidian_path": "Путь", "topics.export.obsidian_path_placeholder": "Выберите путь", "topics.export.obsidian_atributes": "Настроить атрибуты заметки", "topics.export.obsidian_btn": "Подтвердить", "topics.export.obsidian_created": "Дата создания", "topics.export.obsidian_created_placeholder": "Пожалуйста, выберите дату создания", "topics.export.obsidian_export_failed": "Экспорт не удалось", "topics.export.obsidian_export_success": "Экспорт успешно завершен", "topics.export.obsidian_operate": "Метод обработки", "topics.export.obsidian_operate_append": "Добавить в конец", "topics.export.obsidian_operate_new_or_overwrite": "Создать новый (перезаписать, если уже существует)", "topics.export.obsidian_operate_placeholder": "Пожалуйста, выберите метод обработки", "topics.export.obsidian_operate_prepend": "Добавить в начало", "topics.export.obsidian_source": "Источник", "topics.export.obsidian_source_placeholder": "Пожалуйста, введите источник", "topics.export.obsidian_tags": "Тэги", "topics.export.obsidian_tags_placeholder": "Пожалуйста, введите имена тегов. Разделяйте несколько тегов запятыми на английском языке", "topics.export.obsidian_title": "Заголовок", "topics.export.obsidian_title_placeholder": "Пожалуйста, введите заголовок", "topics.export.obsidian_title_required": "Заголовок не может быть пустым", "topics.export.obsidian_no_vaults": "Хранилища Obsidian не найдены", "topics.export.obsidian_loading": "Загрузка...", "topics.export.obsidian_fetch_error": "Не удалось получить хранилища Obsidian", "topics.export.obsidian_fetch_folders_error": "Не удалось получить структуру папок", "topics.export.obsidian_no_vault_selected": "Пожалуйста, сначала выберите хранилище", "topics.export.obsidian_select_vault_first": "Пожалуйста, сначала выберите хранилище", "topics.export.obsidian_root_directory": "Корневая директория", "topics.export.title": "Экспорт", "topics.export.word": "Экспорт как Word", "topics.export.yuque": "Экспорт в Yuque", "topics.list": "Список топиков", "topics.move_to": "Переместить в", "topics.new": "Новый топик", "topics.pinned": "Закрепленные темы", "topics.prompt": "Тематические подсказки", "topics.prompt.edit.title": "Редактировать подсказки темы", "topics.prompt.tips": "Тематические подсказки: Дополнительные подсказки, предоставленные для текущей темы", "topics.title": "Топики", "topics.unpinned": "Открепленные темы", "translate": "Перевести", "topics.export.siyuan": "Экспорт в Siyuan Note", "topics.export.wait_for_title_naming": "Создание заголовка...", "topics.export.obsidian_reasoning": "Включить цепочку рассуждений", "topics.export.title_naming_success": "Заголовок успешно создан", "topics.export.title_naming_failed": "Не удалось создать заголовок, используется заголовок по умолчанию", "input.translating": "Перевод...", "input.upload.upload_from_local": "Загрузить локальный файл...", "input.web_search.builtin": "Модель встроена", "input.web_search.builtin.enabled_content": "Используйте встроенную функцию веб-поиска модели", "input.web_search.builtin.disabled_content": "Текущая модель не поддерживает веб-поиск", "input.web_search.no_web_search": "Отключить веб-поиск", "input.web_search.no_web_search.description": "Отключить веб-поиск", "input.tools.collapse": "Свернуть", "input.tools.expand": "Развернуть", "input.tools.collapse_in": "Свернуть", "input.tools.collapse_out": "Развернуть", "input.thinking": "Мыслим", "input.thinking.mode.default": "По умолчанию", "input.thinking.mode.default.tip": "Модель автоматически определяет количество токенов для размышления", "input.thinking.mode.custom": "Пользовательский", "input.thinking.mode.custom.tip": "Модель может максимально размышлять количество токенов. Необходимо учитывать ограничение контекста модели, иначе будет ошибка", "input.thinking.mode.tokens.tip": "Установите количество токенов для размышления", "input.thinking.budget_exceeds_max": "Бюджет размышления превышает максимальное количество токенов"}, "code_block": {"collapse": "Свернуть", "copy.failed": "Не удалось скопировать", "copy.source": "Копировать исходный код", "copy.success": "Скопировано", "copy": "Копировать", "download.failed.network": "Не удалось скачать. Пожалуйста, проверьте ваше интернет-соединение", "download.png": "Скачать PNG", "download.source": "Скачать исходный код", "download.svg": "Скачать SVG", "download": "Скачать", "edit.save.failed.message_not_found": "Не удалось сохранить изменения, не найдено сообщение", "edit.save.failed": "Не удалось сохранить изменения", "edit.save.success": "Изменения сохранены", "edit.save": "Сохранить изменения", "edit": "Редактировать", "expand": "Развернуть", "more": "<PERSON><PERSON><PERSON>", "preview.copy.image": "Скопировать как изображение", "preview.source": "Смотреть исходный код", "preview.zoom_in": "Увеличить", "preview.zoom_out": "Уменьшить", "preview": "Предварительный просмотр", "run": "Выполнить код", "split.restore": "Вернуться к одному окну", "split": "Разделить на два окна", "wrap.off": "Отменить перенос строки", "wrap.on": "Перенос строки"}, "common": {"add": "Добавить", "advanced_settings": "Дополнительные настройки", "and": "и", "assistant": "Ассистент", "avatar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "back": "Назад", "cancel": "Отмена", "chat": "Чат", "clear": "Очистить", "close": "Закрыть", "confirm": "Подтверждение", "copied": "Скопировано", "copy": "Копировать", "inspect": "Осмотреть", "cut": "Вырезать", "default": "По умолчанию", "delete": "Удалить", "description": "Описание", "docs": "Документы", "download": "Скачать", "duplicate": "Дублировать", "edit": "Редактировать", "expand": "Развернуть", "collapse": "Свернуть", "footnote": "Цитируемый контент", "footnotes": "Сноски", "fullscreen": "Вы вошли в полноэкранный режим. Нажмите F11 для выхода", "knowledge_base": "База знаний", "language": "Язык", "loading": "Загрузка...", "model": "Модель", "models": "Модели", "more": "<PERSON><PERSON><PERSON>", "name": "Имя", "paste": "Вставить", "prompt": "Промпт", "provider": "Провайдер", "regenerate": "Пересоздать", "rename": "Переименовать", "reset": "Сбросить", "save": "Сохранить", "search": "Поиск", "select": "Выбрать", "selectedMessages": "Выбрано {{count}} сообщений", "success": "Успешно", "topics": "Топики", "warning": "Предупреждение", "you": "Вы", "reasoning_content": "Глубокий анализ", "sort": {"pinyin": "Сортировать по пиньинь", "pinyin.asc": "Сортировать по пиньинь (А-Я)", "pinyin.desc": "Сортировать по пиньинь (Я-А)"}, "no_results": "Результатов не найдено"}, "docs": {"title": "Документация"}, "error": {"backup.file_format": "Ошибка формата файла резервной копии", "chat.response": "Что-то пошло не так. Пожалуйста, проверьте, установлен ли ваш ключ API в Настройки > Провайдеры", "http": {"400": "Не удалось выполнить запрос. Пожалуйста, проверьте, правильно ли настроены параметры запроса. Если вы изменили настройки модели, пожалуйста, сбросьте их до значений по умолчанию", "401": "Не удалось пройти аутентификацию. Пожалуйста, проверьте, правильно ли настроен ваш ключ API", "403": "Доступ запрещен. Пожалуйста, проверьте, правильно ли настроены ваши учетные данные или обратитесь к поставщику услуг для получения дополнительной информации", "404": "Модель не найдена или путь запроса неверен", "429": "Слишком много запросов. Пожалуйста, попробуйте позже", "500": "Серверная ошибка. Пожалуйста, попробуйте позже", "502": "Серверная ошибка. Пожалуйста, попробуйте позже", "503": "Серверная ошибка. Пожалуйста, попробуйте позже", "504": "Серверная ошибка. Пожалуйста, попробуйте позже"}, "model.exists": "Модель уже существует", "no_api_key": "Ключ API не настроен", "provider_disabled": "Провайдер моделей не включен", "render": {"description": "Не удалось рендерить содержимое сообщения. Пожалуйста, проверьте, правильно ли формат содержимого сообщения", "title": "Ошибка рендеринга"}, "user_message_not_found": "Не удалось найти исходное сообщение пользователя", "unknown": "Неизвестная ошибка", "pause_placeholder": "Получение ответа приостановлено"}, "export": {"assistant": "Ассистент", "attached_files": "Прикрепленные файлы", "conversation_details": "Детали разговора", "conversation_history": "История разговора", "created": "Создано", "last_updated": "Последнее обновление", "messages": "Сообщения", "user": "Пользователь"}, "files": {"actions": "Действия", "all": "Все файлы", "count": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "Дата создания", "delete": "Удалить", "delete.content": "Удаление файла удалит его из всех сообщений, вы уверены, что хотите удалить этот файл?", "delete.paintings.warning": "В изображениях содержится этот файл, удаление невозможно", "delete.title": "Удалить файл", "document": "Документ", "edit": "Редактировать", "file": "<PERSON>а<PERSON><PERSON>", "image": "Изображение", "name": "Имя", "open": "Открыть", "size": "Размер", "text": "Текст", "title": "Файлы", "type": "Тип"}, "gpustack": {"keep_alive_time.description": "Время в минутах, в течение которого модель остается активной, по умолчанию 5 минут.", "keep_alive_time.placeholder": "Минуты", "keep_alive_time.title": "Время жизни модели", "title": "GPUStack"}, "history": {"continue_chat": "Продолжить чат", "locate.message": "Найти сообщение", "search.messages": "Поиск всех сообщений", "search.placeholder": "Поиск топиков или сообщений...", "search.topics.empty": "Топики не найдены, нажмите Enter для поиска всех сообщений", "title": "Поиск топиков"}, "knowledge": {"add": {"title": "Добавить базу знаний"}, "add_directory": "Добавить директорию", "add_file": "Добавить файл", "add_note": "Добавить запись", "add_sitemap": "Карта сайта", "add_url": "Добавить URL", "cancel_index": "Отменить индексирование", "chunk_overlap": "Перекрытие фрагмента", "chunk_overlap_placeholder": "По умолчанию (не рекомендуется изменять)", "chunk_overlap_tooltip": "Перекрытие фрагмента, не превышающее модель контекста", "chunk_size": "Размер фрагмента", "chunk_size_change_warning": "Размер фрагмента и перекрытие фрагмента могут быть изменены только для новых содержимого", "chunk_size_placeholder": "По умолчанию (не рекомендуется изменять)", "chunk_size_too_large": "Размер фрагмента не может превышать модель контекста ({{max_context}})", "chunk_size_tooltip": "Размер фрагмента, не превышающий модель контекста", "clear_selection": "Очистить выбор", "delete": "Удалить", "delete_confirm": "Вы уверены, что хотите удалить эту базу знаний?", "directories": "Директории", "directory_placeholder": "Введите путь к директории", "document_count": "Количество запрошенных документов", "document_count_default": "По умолчанию", "document_count_help": "Количество запрошенных документов, вместе с ними передается больше информации, но и требуется больше токенов", "drag_file": "Перетащите файл сюда", "edit_remark": "Изменить примечание", "edit_remark_placeholder": "Пожалуйста, введите содержание примечания", "empty": "База знаний не найдена", "file_hint": "Поддерживаются {{file_types}}", "index_all": "Индексировать все", "index_cancelled": "Индексирование отменено", "index_started": "Индексирование началось", "invalid_url": "Неверный URL", "model_info": "Модель информации", "no_bases": "База знаний не найдена", "no_match": "Не найдено содержимого в базе знаний.", "no_provider": "База знаний модель поставщика не настроена, база знаний больше не поддерживается, пожалуйста, создайте новую базу знаний", "not_set": "Не установлено", "not_support": "База знаний базы данных движок обновлен, база знаний больше не поддерживается, пожалуйста, создайте новую базу знаний", "notes": "Заметки", "notes_placeholder": "Введите дополнительную информацию или контекст для этой базы знаний...", "rename": "Переименовать", "search": "Поиск в базе знаний", "search_placeholder": "Введите текст для поиска", "settings": "Настройки базы знаний", "sitemap_placeholder": "Введите URL карты сайта", "sitemaps": "Сайты", "source": "Источник", "status": "Статус", "status_completed": "Завершено", "status_failed": "Ошибка", "status_new": "Добавлено", "status_pending": "Ожидание", "status_processing": "Обработка", "threshold": "Порог соответствия", "threshold_placeholder": "Не установлено", "threshold_too_large_or_small": "Порог не может быть больше 1 или меньше 0", "threshold_tooltip": "Используется для оценки соответствия между пользовательским вопросом и содержимым в базе знаний (0-1)", "title": "База знаний", "topN": "Количество возвращаемых результатов", "topN_too_large_or_small": "Количество возвращаемых результатов не может быть больше 30 или меньше 1.", "topN_placeholder": "Не установлено", "topN_tooltip": "Количество возвращаемых совпадений; чем больше значение, тем больше совпадений, но и потребление токенов тоже возрастает.", "url_added": "URL добавлен", "url_placeholder": "Введите URL, несколько URL через Enter", "urls": "URL-адреса", "dimensions": "векторное пространство", "dimensions_size_tooltip": "Размерность вложения, чем больше значение, тем больше размерность вложения, но и потребляемых токенов также становится больше.", "dimensions_size_placeholder": " Размерность эмбеддинга, например 1024", "dimensions_auto_set": "Автоматическая установка размерности эмбеддинга", "dimensions_error_invalid": "Пожалуйста, введите размерность эмбеддинга", "dimensions_size_too_large": "Размерность вложения не может превышать ограничение контекста модели ({{max_context}})", "dimensions_set_right": "⚠️ Убедитесь, что модель поддерживает заданный размер эмбеддинга", "dimensions_default": "Модель будет использовать размер эмбеддинга по умолчанию"}, "languages": {"arabic": "Арабский", "chinese": "Китайский", "chinese-traditional": "Китайский традиционный", "english": "Английский", "french": "Французский", "german": "Немецкий", "italian": "Итальянский", "japanese": "Японский", "korean": "Корейский", "portuguese": "Португальский", "russian": "Русский", "spanish": "Испанский", "polish": "Польский", "turkish": "Туркменский", "thai": "Тайский", "vietnamese": "Вьетнамский", "indonesian": "Индонезийский", "urdu": "Урду", "malay": "Малайзийский"}, "lmstudio": {"keep_alive_time.description": "Время в минутах, в течение которого модель остается активной, по умолчанию 5 минут.", "keep_alive_time.placeholder": "Минуты", "keep_alive_time.title": "Время жизни модели", "title": "LM Studio"}, "message": {"agents": {"imported": "Импорт успешно выполнен", "import.error": "Импорт не выполнен"}, "api.check.model.title": "Выберите модель для проверки", "api.connection.failed": "Соединение не удалось", "api.connection.success": "Соединение успешно", "assistant.added.content": "Ассистент успешно добавлен", "attachments": {"pasted_image": "Вырезанное изображение", "pasted_text": "Вырезанный текст"}, "backup.failed": "Создание резервной копии не удалось", "backup.start.success": "Создание резервной копии начато", "backup.success": "Резервная копия успешно создана", "chat.completion.paused": "Завершение чата приостановлено", "citation": "{{count}} цитат", "citations": "Содержание цитат", "copied": "Скопировано!", "copy.failed": "Не удалось скопировать", "copy.success": "Скопировано!", "delete.confirm.title": "Подтверждение удаления", "delete.confirm.content": "Вы уверены, что хотите удалить выбранные {{count}} сообщения?", "delete.failed": "Ошибка удаления", "delete.success": "Удаление успешно", "error.chunk_overlap_too_large": "Перекрытие фрагментов не может быть больше размера фрагмента", "empty_url": "Не удалось загрузить изображение, возможно, запрос содержит конфиденциальный контент или запрещенные слова", "error.dimension_too_large": "Размер содержимого слишком велик", "error.enter.api.host": "Пожалуйста, введите ваш API хост", "error.enter.api.key": "Пожалуйста, введите ваш API ключ", "error.enter.model": "Пожалуйста, выберите модель", "error.enter.name": "Пожалуйста, введите название базы знаний", "error.get_embedding_dimensions": "Не удалось получить размерность встраивания", "error.invalid.api.host": "Неверный API адрес", "error.invalid.api.key": "Неверный API ключ", "error.invalid.enter.model": "Пожалуйста, выберите модель", "error.invalid.proxy.url": "Неверный URL прокси", "error.invalid.webdav": "Неверные настройки WebDAV", "error.joplin.export": "Не удалось экспортировать в Joplin, пожалуйста, убедитесь, что Joplin запущен и проверьте состояние подключения или настройки", "error.joplin.no_config": "Joplin Authorization Token или URL не настроен", "error.invalid.nutstore": "Неверные настройки Nutstore", "error.invalid.nutstore_token": "Неверный Nutstore токен", "error.markdown.export.preconf": "Не удалось экспортировать файл Markdown в предуказанный путь", "error.markdown.export.specified": "Не удалось экспортировать файл Markdown", "error.notion.export": "Ошибка экспорта в Notion, пожалуйста, проверьте состояние подключения и настройки в документации", "error.notion.no_api_key": "Notion ApiKey или Notion DatabaseID не настроен", "error.yuque.export": "Ошибка экспорта в Yuque, пожалуйста, проверьте состояние подключения и настройки в документации", "error.yuque.no_config": "Yuque Token или Yuque Url не настроен", "group.delete.content": "Удаление группы сообщений удалит пользовательский вопрос и все ответы помощника", "group.delete.title": "Удалить группу сообщений", "ignore.knowledge.base": "Режим сети включен, игнорировать базу знаний", "info.notion.block_reach_limit": "Диалог слишком длинный, экспортируется в Notion по страницам", "loading.notion.exporting_progress": "Экспорт в Notion ({{current}}/{{total}})...", "loading.notion.preparing": "Подготовка к экспорту в Notion...", "mention.title": "Переключить модель ответа", "message.code_style": "Стиль кода", "message.delete.content": "Вы уверены, что хотите удалить это сообщение?", "message.delete.title": "Удалить сообщение", "message.multi_model_style": "Стиль ответов от нескольких моделей", "message.multi_model_style.fold": "Вкладки", "message.multi_model_style.fold.compress": "Переключить на компактный макет", "message.multi_model_style.fold.expand": "Переключить на расширенный макет", "message.multi_model_style.grid": "Карточки", "message.multi_model_style.horizontal": "Горизонтальное расположение", "message.multi_model_style.vertical": "Вертикальное расположение", "message.style": "Стиль сообщения", "message.style.bubble": "Пузырь", "message.style.plain": "Простой", "processing": "Обрабатывается...", "regenerate.confirm": "Перегенерация заменит текущее сообщение", "reset.confirm.content": "Вы уверены, что хотите очистить все данные?", "reset.double.confirm.content": "Все данные будут утеряны, хотите продолжить?", "reset.double.confirm.title": "ДАННЫЕ БУДУТ УТЕРЯНЫ !!!", "restore.failed": "Восстановление не удалось", "restore.success": "Успешно восстановлено", "save.success.title": "Успешно сохранено", "searching": "Идет поиск...", "success.joplin.export": "Успешный экспорт в Joplin", "success.markdown.export.preconf": "Файл Markdown успешно экспортирован в предуказанный путь", "success.markdown.export.specified": "Файл Markdown успешно экспортирован", "success.notion.export": "Успешный экспорт в Notion", "success.yuque.export": "Успешный экспорт в Yuque", "switch.disabled": "Пожалуйста, дождитесь завершения текущего ответа", "tools": {"completed": "Завершено", "invoking": "Вы<PERSON><PERSON>", "error": "Произошла ошибка", "raw": "Исходный", "preview": "Предпросмотр"}, "topic.added": "Новый топик добавлен", "upgrade.success.button": "Перезапустить", "upgrade.success.content": "Пожалуйста, перезапустите приложение для завершения обновления", "upgrade.success.title": "Обновление успешно", "warn.notion.exporting": "Экспортируется в Notion, пожалуйста, не отправляйте повторные запросы!", "warning.rate.limit": "Отправка слишком частая, пожалуйста, подождите {{seconds}} секунд, прежде чем попробовать снова.", "error.siyuan.export": "Ошибка экспорта в Siyuan, пожалуйста, проверьте состояние подключения и настройки в документации", "error.siyuan.no_config": "Не настроен API адрес или токен Si<PERSON>", "success.siyuan.export": "Успешный экспорт в Siyuan", "warn.yuque.exporting": "Экспортируется в Yuque, пожалуйста, не отправляйте повторные запросы!", "warn.siyuan.exporting": "Экспортируется в Siyuan, пожалуйста, не отправляйте повторные запросы!", "download.success": "Скачано успешно", "download.failed": "Скачивание не удалось", "error.fetchTopicName": "Не удалось назвать топик"}, "minapp": {"popup": {"refresh": "Обновить", "close": "Закрыть встроенное приложение", "minimize": "Свернуть встроенное приложение", "goBack": "Назад", "goForward": "Вперед", "devtools": "Инструменты разработчика", "openExternal": "Открыть в браузере", "rightclick_copyurl": "ПКМ → Копировать URL", "open_link_external_on": "Текущий: Открыть ссылки в браузере", "open_link_external_off": "Текущий: Открыть ссылки в окне по умолчанию"}, "sidebar": {"add": {"title": "Добавить в боковую панель"}, "remove": {"title": "Удалить из боковой панели"}, "remove_custom": {"title": "Удалить пользовательское приложение"}, "hide": {"title": "Скрыть"}, "close": {"title": "Закрыть"}, "closeall": {"title": "Закрыть все"}}, "title": "Встроенные приложения"}, "miniwindow": {"clipboard": {"empty": "Буфер обмена пуст"}, "feature": {"chat": "Ответить на этот вопрос", "explanation": "Объяснение", "summary": "Содержание", "translate": "Текст перевод"}, "footer": {"copy_last_message": "Нажмите C для копирования", "esc": "Нажмите ESC {{action}}", "esc_back": "возвращения", "esc_close": "закрытия окна", "backspace_clear": "Нажмите Backspace, чтобы очистить"}, "input": {"placeholder": {"empty": "Задайте вопрос {{model}}...", "title": "Что вы хотите сделать с этим текстом?"}}, "tooltip": {"pin": "Верхнее окно"}}, "models": {"add_parameter": "Добавить параметр", "all": "Все", "custom_parameters": "Пользовательские параметры", "dimensions": "{{dimensions}} мер", "edit": "Редактировать модель", "embedding": "Встраиваемые", "embedding_model": "Встраиваемые модели", "embedding_model_tooltip": "Добавьте в настройки->модель сервиса->управление", "function_calling": "Вызов функции", "no_matches": "Нет доступных моделей", "parameter_name": "Имя параметра", "parameter_type": {"boolean": "Логическое", "json": "JSON", "number": "Число", "string": "Текст"}, "pinned": "Закреплено", "rerank_model": "Модель переупорядочивания", "rerank_model_support_provider": "Текущая модель переупорядочивания поддерживается только некоторыми поставщиками ({{provider}})", "rerank_model_tooltip": "В настройках -> Служба модели нажмите кнопку \"Управление\", чтобы добавить.", "search": "Поиск моделей...", "stream_output": "Потоковый вывод", "enable_tool_use": "Вызов инструмента", "type": {"embedding": "Встраиваемые", "free": "Бесплатные", "function_calling": "Инструкция", "reasoning": "Рассуждение", "rerank": "Переупорядочить", "select": "Выберите тип модели", "text": "Текст", "vision": "Визуальные", "websearch": "Веб-поисковые"}, "rerank_model_not_support_provider": "В настоящее время модель переупорядочивания не поддерживает этого провайдера ({{provider}})"}, "navbar": {"expand": "Развернуть диалоговое окно", "hide_sidebar": "Скрыть боковую панель", "show_sidebar": "Показать боковую панель"}, "notification": {"assistant": "Ответ ассистента", "knowledge.success": "Успешно добавлено {{type}} в базу знаний", "knowledge.error": "Не удалось добавить {{type}} в базу знаний: {{error}}"}, "ollama": {"keep_alive_time.description": "Время в минутах, в течение которого модель остается активной, по умолчанию 5 минут.", "keep_alive_time.placeholder": "Минуты", "keep_alive_time.title": "Время жизни модели", "title": "Ollama"}, "paintings": {"button.delete.image": "Удалить изображение", "button.delete.image.confirm": "Вы уверены, что хотите удалить это изображение?", "button.new.image": "Новое изображение", "guidance_scale": "Масштаб руководства", "guidance_scale_tip": "Без классификатора руководства. Насколько близко вы хотите, чтобы модель придерживалась вашего промпта при поиске связанного изображения для показа вам", "image.size": "Размер изображения", "inference_steps": "Шаги вывода", "inference_steps_tip": "Количество шагов вывода для выполнения. Больше шагов производят более высокое качество, но занимают больше времени", "negative_prompt": "Негативный промпт", "negative_prompt_tip": "Опишите, что вы не хотите включать в изображение", "number_images": "Количество изображений", "number_images_tip": "Количество изображений для генерации (1-4)", "prompt_enhancement": "Улучшение промпта", "prompt_enhancement_tip": "При включении переписывает промпт в более детальную, модель-ориентированную версию", "prompt_placeholder": "Опишите изображение, которое вы хотите создать, например, Спокойное озеро на закате с горами на заднем плане", "regenerate.confirm": "Это заменит ваши существующие сгенерированные изображения. Хотите продолжить?", "seed": "Ключ генерации", "seed_tip": "Одинаковый ключ генерации и промпт могут производить похожие изображения", "seed_desc_tip": "Одинаковые сиды и промпты могут генерировать похожие изображения, установка -1 будет создавать разные результаты каждый раз", "title": "Изображения", "magic_prompt_option": "Улучшение промпта", "model": "Модель", "aspect_ratio": "Пропорции изображения", "style_type": "Стиль", "rendering_speed": "Скорость рендеринга", "learn_more": "Узнать больше", "prompt_placeholder_edit": "Введите ваше описание изображения, текстовая отрисовка использует двойные кавычки для обертки", "prompt_placeholder_en": "Введите” английский “описание изображения, текстовая отрисовка использует двойные кавычки для обертки", "paint_course": "Руководство / Учебник", "proxy_required": "Открыть прокси и включить “TUN режим” для просмотра сгенерированных изображений или скопировать их в браузер для открытия. В будущем будет поддерживаться прямое соединение", "image_file_required": "Пожалуйста, сначала загрузите изображение", "image_file_retry": "Пожалуйста, сначала загрузите изображение", "image_placeholder": "Изображение недоступно", "image_retry": "Повторить", "translating": "Перевод...", "style_types": {"auto": "Авто", "general": "Общий", "realistic": "Реалистичный", "design": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3d": "3D", "anime": "Аниме"}, "quality_options": {"auto": "Авто", "low": "Низкое", "medium": "Среднее", "high": "Высокое"}, "moderation_options": {"auto": "Авто", "low": "Низкое"}, "background_options": {"auto": "Авто", "transparent": "Прозрачный", "opaque": "Непрозрачный"}, "rendering_speeds": {"default": "По умолчанию", "turbo": "Быстро", "quality": "Качественно"}, "aspect_ratios": {"square": "Ква<PERSON><PERSON><PERSON><PERSON>", "portrait": "Портрет", "landscape": "Пей<PERSON><PERSON><PERSON>"}, "person_generation_options": {"allow_all": "Разрешено все", "allow_adult": "Разрешено взрослые", "allow_none": "Не разрешено"}, "quality": "Качество", "moderation": "Сенсорность", "background": "Фон", "mode": {"generate": "Рисование", "edit": "Редактирование", "remix": "Смешивание", "upscale": "Увеличение"}, "generate": {"model_tip": "Версия модели: V2 - новейшая API модель, V2A - быстрая модель, V_1 - первое поколение, _TURBO - ускоренная версия", "number_images_tip": "Количество изображений для одновременной генерации", "seed_tip": "Контролирует случайность генерации изображений для воспроизведения одинаковых результатов", "negative_prompt_tip": "Описывает, что вы не хотите видеть в изображении", "magic_prompt_option_tip": "Интеллектуально оптимизирует подсказки для улучшения эффекта генерации", "style_type_tip": "Стиль генерации изображений, доступен только для версий V_2 и выше", "rendering_speed_tip": "Управляет балансом между скоростью рендеринга и качеством, доступно только для V_3", "person_generation": "Генерация персонажа", "person_generation_tip": "Разрешить модель генерировать изображения людей"}, "edit": {"image_file": "Изображение для редактирования", "model_tip": "Частичное редактирование поддерживается только версиями V_2 и V_2_TURBO", "number_images_tip": "Количество результатов редактирования для генерации", "style_type_tip": "Стиль изображения после редактирования, доступен только для версий V_2 и выше", "seed_tip": "Контролирует случайность результатов редактирования", "magic_prompt_option_tip": "Интеллектуально оптимизирует подсказки для улучшения эффекта редактирования", "rendering_speed_tip": "Управляет балансом между скоростью рендеринга и качеством, доступно только для V_3"}, "remix": {"model_tip": "Выберите версию AI модели для ремикса", "image_file": "Референсное изображение", "image_weight": "Вес референсного изображения", "image_weight_tip": "Регулирует степень влияния референсного изображения", "number_images_tip": "Количество результатов ремикса для генерации", "seed_tip": "Контролирует случайность результатов ремикса", "style_type_tip": "Стиль изображения после ремикса, доступен только для версий V_2 и выше", "negative_prompt_tip": "Описывает, что вы не хотите видеть в результатах ремикса", "magic_prompt_option_tip": "Интеллектуально оптимизирует подсказки для улучшения эффекта ремикса", "rendering_speed_tip": "Управляет балансом между скоростью рендеринга и качеством, доступно только для V_3"}, "upscale": {"image_file": "Изображение для увеличения", "resemblance": "Сходство", "resemblance_tip": "Насколько близко результат увеличения к исходному изображению", "detail": "Детали", "detail_tip": "Насколько детально увеличенное изображение", "number_images_tip": "Количество увеличенных результатов для генерации", "seed_tip": "Контролирует случайный характер увеличения изображений для воспроизводимых результатов", "magic_prompt_option_tip": "Улучшает увеличение изображений с помощью интеллектуального оптимизирования промптов"}, "text_desc_required": "Пожалуйста, сначала введите описание изображения", "image_handle_required": "Пожалуйста, сначала загрузите изображение.", "req_error_text": "Операция не удалась, повторите попытку. Пожалуйста, избегайте защищенных авторским правом терминов и конфиденциальных слов в запросах.", "req_error_token": "Пожалуйста, проверьте действительность токена", "req_error_no_balance": "Пожалуйста, проверьте действительность токена", "auto_create_paint": "Автоматическое создание изображения", "auto_create_paint_tip": "После генерации изображения будет автоматически создано новое.", "select_model": "Выбрать модель", "input_parameters": "Ввести параметры", "input_image": "Входное изображение", "generated_image": "Сгенерированное изображение", "pricing": "Цены", "model_and_pricing": "Модель и цены", "per_image": "за изображение", "per_images": "за изображения", "required_field": "Обязательное поле", "uploaded_input": "Загруженный ввод"}, "prompts": {"explanation": "Объясните мне этот концепт", "summarize": "Суммируйте этот текст", "title": "Вы - эксперт в общении, который суммирует разговоры пользователя в 10-символьном заголовке, совпадающем с языком пользователя, без использования знаков препинания и других специальных символов"}, "provider": {"aihubmix": "AiHubMix", "burncloud": "BurnCloud", "alayanew": "Alaya NeW", "anthropic": "Anthropic", "azure-openai": "Azure OpenAI", "baichuan": "Baichuan", "baidu-cloud": "Baidu Cloud", "cephalon": "<PERSON><PERSON><PERSON>", "copilot": "GitHub Copilot", "dashscope": "Alibaba Cloud", "deepseek": "DeepSeek", "dmxapi": "DMXAPI", "doubao": "Volcengine", "fireworks": "Fireworks", "gemini": "Gemini", "gitee-ai": "Gitee AI", "github": "GitHub Models", "gpustack": "GPUStack", "grok": "Grok", "groq": "Groq", "hunyuan": "<PERSON>cent <PERSON>", "hyperbolic": "Hyperbolic", "infini": "Infini", "jina": "<PERSON><PERSON>", "lmstudio": "LM Studio", "minimax": "MiniMax", "mistral": "<PERSON><PERSON><PERSON>", "modelscope": "ModelScope", "moonshot": "Moonshot", "nvidia": "Nvidia", "o3": "O3", "ocoolai": "ocoolAI", "ollama": "Ollama", "openai": "OpenAI", "openrouter": "OpenRouter", "perplexity": "Perplexity", "ppio": "PPIO", "qwenlm": "QwenLM", "silicon": "SiliconFlow", "stepfun": "<PERSON><PERSON><PERSON>", "tencent-cloud-ti": "Tencent Cloud TI", "together": "Together", "xirang": "State Cloud Xirang", "yi": "<PERSON>", "zhinao": "360AI", "zhipu": "ZHIPU AI", "voyageai": "Voyage AI", "qiniu": "<PERSON><PERSON>", "tokenflux": "TokenFlux", "302ai": "302.AI", "lanyun": "LANYUN"}, "restore": {"confirm": "Вы уверены, что хотите восстановить данные?", "confirm.button": "Выбрать файл резервной копии", "content": "Операция восстановления перезапишет все текущие данные приложения данными из резервной копии. Это может занять некоторое время.", "progress": {"completed": "Восстановление завершено", "copying_files": "Копирование файлов... {{progress}}%", "extracting": "Распаковка резервной копии...", "preparing": "Подготовка к восстановлению...", "reading_data": "Чтение данных...", "title": "Прогресс восстановления"}, "title": "Восстановление данных"}, "settings": {"about": "О программе и обратная связь", "about.checkingUpdate": "Проверка обновлений...", "about.checkUpdate": "Проверить обновления", "about.checkUpdate.available": "Обновить", "about.contact.button": "Электронная почта", "about.contact.title": "Контакты", "about.description": "Мощный AI-ассистент для созидания", "about.downloading": "Загрузка...", "about.feedback.button": "Обратная связь", "about.feedback.title": "Обратная связь", "about.license.button": "Лицензия", "about.license.title": "Лицензия", "about.releases.button": "Релизы", "about.releases.title": "Заметки о релизах", "about.social.title": "Социальные аккаунты", "about.title": "О программе", "about.updateAvailable": "Найдено новое обновление {{version}}", "about.updateError": "Ошибка обновления", "about.updateNotAvailable": "Вы используете последнюю версию", "about.website.button": "Сайт", "about.website.title": "Официал<PERSON>ный сайт", "advanced.auto_switch_to_topics": "Автоматически переключаться на топик", "advanced.title": "Расширенные настройки", "assistant": "Ассистент по умолчанию", "assistant.model_params": "Параметры модели", "assistant.icon.type": "Тип модели иконки", "assistant.icon.type.model": "Модель иконки", "assistant.icon.type.emoji": "Emoji иконка", "assistant.icon.type.none": "Не отображать", "assistant.title": "Ассистент по умолчанию", "data": {"app_data": "Данные приложения", "app_knowledge": "База знаний", "app_knowledge.button.delete": "Удалить файл", "app_knowledge.remove_all": "Удалить файлы базы знаний", "app_knowledge.remove_all_confirm": "Удаление файлов базы знаний не удалит саму базу знаний, что позволит уменьшить занимаемый объем памяти, продолжить?", "app_knowledge.remove_all_success": "Файлы удалены успешно", "app_logs": "Логи приложения", "backup.skip_file_data_title": "Упрощенная резервная копия", "backup.skip_file_data_help": "Пропустить при резервном копировании такие данные, как изображения, базы знаний и другие файлы данных, и сделать резервную копию только переписки и настроек. Это уменьшает использование места на диске и ускоряет процесс резервного копирования.", "clear_cache": {"button": "Очистка кэша", "confirm": "Очистка кэша удалит данные приложения. Это действие необратимо, продолжить?", "error": "Ошибка при очистке кэша", "success": "Кэш очищен", "title": "Очистка кэша"}, "data.title": "Каталог данных", "divider.basic": "Основные настройки данных", "divider.cloud_storage": "Настройки облачного резервирования", "divider.export_settings": "Настройки экспорта", "divider.third_party": "Сторонние подключения", "hour_interval_one": "{{count}} час", "hour_interval_other": "{{count}} ч<PERSON><PERSON><PERSON>", "export_menu": {"title": "Настройки меню экспорта", "image": "Экспорт как изображение", "markdown": "Экспорт в Markdown", "markdown_reason": "Экспорт в Markdown (с рассуждениями)", "notion": "Экспорт в Notion", "yuque": "Экспорт в Yuque", "obsidian": "Экспорт в Obsidian", "siyuan": "Экспорт в SiYuan Note", "joplin": "Экспорт в Joplin", "docx": "Экспорт в Word"}, "joplin": {"check": {"button": "Проверить", "empty_token": "Сначала введите токен Joplin", "empty_url": "Сначала введите URL Joplin", "fail": "Не удалось проверить подключение к Joplin", "success": "Подключение к Jo<PERSON>lin успешно проверено"}, "help": "Включите Joplin опцию, проверьте порт и скопируйте токен", "title": "Настройка Joplin", "token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "token_placeholder": "Введите токен <PERSON>", "url": "URL Joplin", "url_placeholder": "http://127.0.0.1:41184/", "export_reasoning.title": "Включить цепочку рассуждений при экспорте", "export_reasoning.help": "Если включено, экспортируемый контент будет содержать цепочку рассуждений, сгенерированную ассистентом."}, "markdown_export.force_dollar_math.help": "Если включено, при экспорте в Markdown для обозначения формул LaTeX будет принудительно использоваться $$. Примечание: Эта опция также влияет на все методы экспорта через Markdown, такие как Notion, Yuque и т.д.", "markdown_export.force_dollar_math.title": "Принудительно использовать $$ для формул LaTeX", "markdown_export.help": "Если указано, файлы будут автоматически сохраняться в этот путь; в противном случае появится диалоговое окно сохранения.", "markdown_export.path": "Путь экспорта по умолчанию", "markdown_export.path_placeholder": "Путь экспорта", "markdown_export.select": "Выбрать", "markdown_export.title": "Экспорт в Markdown", "markdown_export.show_model_name.title": "Использовать имя модели при экспорте", "markdown_export.show_model_name.help": "Если включено, для создания заголовков экспортируемых сообщений будет использоваться модель именования темы. Примечание: Эта опция также влияет на все методы экспорта через Markdown, такие как Notion, Yuque и т.д.", "markdown_export.show_model_provider.title": "Показать поставщика модели", "markdown_export.show_model_provider.help": "Показывать поставщика модели (например, OpenAI, Gemini) при экспорте в Markdown", "minute_interval_one": "{{count}} минута", "minute_interval_other": "{{count}} минут", "notion.api_key": "Ключ API Notion", "notion.api_key_placeholder": "Введите ключ API Notion", "notion.check": {"button": "Проверить", "empty_api_key": "Не настроен API key", "empty_database_id": "Не настроен Database ID", "error": "Аномалия в подключении, пожалуйста, проверьте настройки сети, а также правильность API key и Database ID", "fail": "Не удалось подключиться, пожалуйста, проверьте сеть и правильность API key и Database ID", "success": "Подключение успешно"}, "notion.database_id": "ID базы данных Notion", "notion.database_id_placeholder": "Введите ID базы данных Notion", "notion.help": "Документация по настройке Notion", "notion.page_name_key": "Название поля заголовка страницы", "notion.page_name_key_placeholder": "Введите название поля заголовка страницы, по умолчанию Name", "notion.title": "Настройки Notion", "notion.export_reasoning.title": "Включить цепочку рассуждений при экспорте", "notion.export_reasoning.help": "При включении, содержимое цепочки рассуждений будет включено при экспорте в Notion.", "title": "Настройки данных", "webdav": {"autoSync": "Автоматическое резервное копирование", "autoSync.off": "Выключено", "backup.button": "Резервное копирование на WebDAV", "backup.modal.filename.placeholder": "Введите имя файла резервной копии", "backup.modal.title": "Резервное копирование на WebDAV", "backup.manager.title": "Управление резервными копиями", "backup.manager.refresh": "Обновить", "backup.manager.delete.selected": "Удалить выбранные", "backup.manager.delete.text": "Удалить", "backup.manager.restore.text": "Восстановить", "backup.manager.restore.success": "Восстановление прошло успешно, приложение скоро обновится", "backup.manager.restore.error": "Ошибка восстановления", "backup.manager.delete.confirm.title": "Подтверждение удаления", "backup.manager.delete.confirm.single": "Вы уверены, что хотите удалить резервную копию \"{{fileName}}\"? Это действие нельзя отменить.", "backup.manager.delete.confirm.multiple": "Вы уверены, что хотите удалить {{count}} выбранных резервных копий? Это действие нельзя отменить.", "backup.manager.delete.success.single": "Успешно удалено", "backup.manager.delete.success.multiple": "Успешно удалено {{count}} резервных копий", "backup.manager.delete.error": "Ошибка удаления", "backup.manager.fetch.error": "Ошибка получения файлов резервных копий", "backup.manager.select.files.delete": "Выберите файлы резервных копий для удаления", "backup.manager.columns.fileName": "Имя файла", "backup.manager.columns.modifiedTime": "Время изменения", "backup.manager.columns.size": "Размер", "backup.manager.columns.actions": "Действия", "host": "Хост WebDAV", "host.placeholder": "http://localhost:8080", "hour_interval_one": "{{count}} час", "hour_interval_other": "{{count}} ч<PERSON><PERSON><PERSON>", "lastSync": "Последняя синхронизация", "minute_interval_one": "{{count}} минута", "minute_interval_other": "{{count}} минут", "noSync": "Ожидание следующего резервного копирования", "password": "Пароль WebDAV", "path": "Путь WebDAV", "path.placeholder": "/backup", "restore.button": "Восстановление с WebDAV", "restore.confirm.content": "Восстановление с WebDAV перезапишет текущие данные, продолжить?", "restore.confirm.title": "Подтверждение восстановления", "restore.content": "Восстановление с WebDAV перезапишет текущие данные, продолжить?", "restore.modal.select.placeholder": "Выберите файл резервной копии для восстановления", "restore.modal.title": "Восстановление с WebDAV", "restore.title": "Восстановление с WebDAV", "syncError": "Ошибка резервного копирования", "syncStatus": "Статус резервного копирования", "title": "WebDAV", "user": "Пользователь WebDAV", "maxBackups": "Максимальное количество резервных копий", "maxBackups.unlimited": "Без ограничений"}, "yuque": {"check": {"button": "Проверить", "empty_repo_url": "Сначала введите URL базы знаний", "empty_token": "Сначала введите токен Yuque", "fail": "Не удалось проверить подключение к Yuque", "success": "Подключение к Yuque успешно проверено"}, "help": "Получить токен Yuque", "repo_url": "URL базы знаний", "repo_url_placeholder": "https://www.yuque.com/username/xxx", "title": "Настройка Yuque", "token": "Токен Yuque", "token_placeholder": "Введите токен Yuque"}, "obsidian": {"title": "Настройки Obsidian", "default_vault": "Хранилище Obsidian по умолчанию", "default_vault_placeholder": "Выберите хранилище Obsidian по умолчанию", "default_vault_loading": "Получение хранилищ Obsidian...", "default_vault_no_vaults": "Хранилища Obsidian не найдены", "default_vault_fetch_error": "Не удалось получить хранилища Obsidian", "default_vault_export_failed": "Ошибка экспорта"}, "siyuan": {"title": "Конфигурация SiYuan Note", "api_url": "API адрес", "api_url_placeholder": "Например: http://127.0.0.1:6806", "token": "API токен", "token.help": "Получите в SiYuan Note -> Настройки -> О программе", "token_placeholder": "Введите токен SiYuan Note", "box_id": "ID блокнота", "box_id_placeholder": "Введите ID блокнота", "root_path": "Корневой путь документа", "root_path_placeholder": "Например: /CherryStudio", "check": {"title": "Проверка соединения", "button": "Проверить", "empty_config": "Пожалуйста, заполните API адрес и токен", "success": "Соединение успешно", "fail": "Не удалось подключиться, проверьте API адрес и токен", "error": "Ошибка соединения, проверьте сетевое подключение"}}, "nutstore": {"title": "Настройки Nutstore", "isLogin": "Выполнен вход", "notLogin": "Вход не выполнен", "login.button": "Войти", "logout.button": "Выйти", "logout.title": "Вы уверены, что хотите выйти из Nutstore?", "logout.content": "После выхода вы не сможете создавать резервные копии в Nutstore или восстанавливать данные из Nutstore.", "checkConnection.name": "Проверить соединение", "checkConnection.success": "Подключение к Nutstore установлено", "checkConnection.fail": "Ошибка подключения к Nutstore", "username": "Имя пользователя Nutstore", "path": "Путь хранения Nutstore", "path.placeholder": "Введите путь хранения Nutstore", "backup.button": "Резервное копирование в Nutstore", "restore.button": "Восстановление из Nutstore", "pathSelector.title": "Путь хранения Nutstore", "pathSelector.return": "Назад", "pathSelector.currentPath": "Текущий путь", "new_folder.button.confirm": "Подтвердить", "new_folder.button.cancel": "Отмена", "new_folder.button": "Новая папка"}, "message_title.use_topic_naming.title": "Использовать модель именования тем для создания заголовков сообщений", "message_title.use_topic_naming.help": "Этот параметр влияет на все методы экспорта в Markdown, такие как Notion, <PERSON><PERSON> и т.д."}, "display.assistant.title": "Настройки ассистентов", "display.custom.css": "Пользовательский CSS", "display.custom.css.cherrycss": "Получить из cherrycss.com", "display.custom.css.placeholder": "/* Здесь введите пользовательский CSS */", "display.sidebar.chat.hiddenMessage": "Помощник является базовой функцией и не поддерживает скрытие", "display.sidebar.disabled": "Скрыть иконки", "display.sidebar.empty": "Перетащите скрываемую функцию с левой стороны сюда", "display.sidebar.files.icon": "Показывать иконку файлов", "display.sidebar.knowledge.icon": "Показывать иконку знаний", "display.sidebar.minapp.icon": "Показывать иконку мини-приложения", "display.sidebar.painting.icon": "Показывать иконку рисования", "display.sidebar.title": "Настройки боковой панели", "display.sidebar.translate.icon": "Показывать иконку перевода", "display.sidebar.visible": "Показывать иконки", "display.title": "Настройки отображения", "display.zoom.title": "Настройки масштаба", "display.topic.title": "Настройки топиков", "miniapps": {"title": "Настройки мини-приложений", "disabled": "Скрытые мини-приложения", "empty": "Перетащите мини-приложения слева, чтобы скрыть их", "visible": "Отображаемые мини-приложения", "open_link_external": {"title": "Открывать новые окна в браузере"}, "cache_settings": "Настройки кэша", "cache_title": "Количество кэшируемых мини-приложений", "cache_description": "Установить максимальное количество активных мини-приложений в памяти", "reset_tooltip": "Сбросить до значения по умолчанию", "display_title": "Настройки отображения мини-приложений", "sidebar_title": "Отображение активных мини-приложений в боковой панели", "sidebar_description": "Настройка отображения активных мини-приложений в боковой панели", "cache_change_notice": "Изменения вступят в силу, когда количество открытых мини-приложений достигнет установленного значения", "custom": {"save_success": "Пользовательское мини-приложение успешно сохранено.", "save_error": "Не удалось сохранить пользовательское мини-приложение.", "logo_upload_success": "Логотип успешно загружен.", "logo_upload_error": "Не удалось загрузить логотип.", "title": "Пользовательские мини-приложения", "edit_title": "Редактировать пользовательское мини-приложение", "id": "ID", "remove_success": "Мини-приложение успешно удалено.", "remove_error": "Не удалось удалить мини-приложение.", "id_error": "ID обязателен.", "id_placeholder": "Введите ID", "name": "Имя", "name_error": "Имя обязательно.", "name_placeholder": "Введите имя", "url": "URL", "url_error": "URL обязателен.", "url_placeholder": "Введите URL", "logo": "Лого<PERSON>ип", "logo_url": "URL логотипа", "logo_file": "Загрузить файл логотипа", "logo_url_label": "URL логотипа", "logo_url_placeholder": "Введите URL логотипа", "logo_upload_label": "Загрузить логотип", "logo_upload_button": "Загрузить", "save": "Сохранить", "edit_description": "Здесь вы можете редактировать конфигурации пользовательских мини-приложений. Каждое приложение должно содержать поля id, name, url и logo.", "placeholder": "Введите конфигурацию мини-приложения (формат JSON)", "duplicate_ids": "Найдены повторяющиеся ID: {{ids}}", "conflicting_ids": "Конфликт ID с приложениями по умолчанию: {{ids}}"}}, "font_size.title": "Размер шрифта сообщений", "general": "Общие настройки", "general.avatar.reset": "Сброс аватара", "general.backup.button": "Резервное копирование", "general.backup.title": "Резервное копирование и восстановление данных", "general.display.title": "Настройки отображения", "general.emoji_picker": "Выбор эмодзи", "general.image_upload": "Загрузка изображений", "general.reset.button": "Сброс", "general.reset.title": "Сброс данных", "general.restore.button": "Восстановление", "general.title": "Общие настройки", "general.user_name": "Имя пользователя", "general.user_name.placeholder": "Введите ваше имя", "general.view_webdav_settings": "Просмотр настроек WebDAV", "input.auto_translate_with_space": "Быстрый перевод с помощью 3-х пробелов", "input.target_language": "Целевой язык", "input.target_language.chinese": "Китайский упрощенный", "input.target_language.chinese-traditional": "Китайский традиционный", "input.target_language.english": "Английский", "input.target_language.japanese": "Японский", "input.target_language.russian": "Русский", "launch.onboot": "Автозапуск при включении", "launch.title": "Запуск", "launch.totray": "Свернуть в трей при запуске", "mcp": {"actions": "Действия", "active": "Акти<PERSON><PERSON>н", "addError": "Ошибка добавления сервера", "addServer": "Добавить сервер", "addServer.create": "Быстрое создание", "addServer.importFrom": "Импорт из JSON", "addServer.importFrom.tooltip": "Скопируйте JSON-конфигурацию (приоритет NPX или UVX конфигураций) со страницы введения MCP Servers и вставьте ее в поле ввода.", "addServer.importFrom.placeholder": "Вставьте JSON-конфигурацию сервера MCP", "addServer.importFrom.invalid": "Неверный ввод, проверьте формат JSON", "addServer.importFrom.nameExists": "Сервер уже существует: {{name}}", "addServer.importFrom.oneServer": "Можно сохранить только один конфигурационный файл MCP", "addServer.importFrom.connectionFailed": "Сбой подключения", "addSuccess": "Сервер успешно добавлен", "args": "Аргументы", "argsTooltip": "Каждый аргумент с новой строки", "baseUrlTooltip": "Адрес удаленного URL", "command": "Команда", "sse": "События, отправляемые сервером (sse)", "streamableHttp": "Потоковый HTTP (streamableHttp)", "stdio": "Стандартный ввод/вывод (stdio)", "inMemory": "Память", "config_description": "Настройка серверов протокола контекста модели", "disable": "Отключить сервер MCP", "disable.description": "Не включать функциональность сервера MCP", "deleteError": "Не удалось удалить сервер", "deleteSuccess": "Сервер успешно удален", "dependenciesInstall": "Установить зависимости", "dependenciesInstalling": "Установка зависимостей...", "description": "Описание", "noDescriptionAvailable": "Описание отсутствует", "duplicateName": "Сервер с таким именем уже существует", "editJson": "Редактировать JSON", "editServer": "Редактировать сервер", "env": "Переменные окружения", "envTooltip": "Формат: KEY=value, по одной на строку", "headers": "Заголовки", "headersTooltip": "Пользовательские заголовки для HTTP-запросов", "findMore": "Найти больше MCP", "searchNpx": "Найти MCP", "install": "Установить", "installError": "Не удалось установить зависимости", "installSuccess": "Зависимости успешно установлены", "jsonFormatError": "Ошибка форматирования JSON", "jsonModeHint": "Редактируйте JSON-форматирование конфигурации сервера MCP. Перед сохранением убедитесь, что формат правильный.", "jsonSaveError": "Не удалось сохранить конфигурацию JSON", "jsonSaveSuccess": "JSON конфигурация сохранена", "missingDependencies": "отсутствует, пожалуйста, установите для продолжения.", "name": "Имя", "noServers": "Серверы не настроены", "newServer": "MCP сервер", "npx_list": {"actions": "Действия", "description": "Описание", "no_packages": "Ничего не найдено", "npm": "NPM", "package_name": "Имя пакета", "scope_placeholder": "Введите область npm (например, @your-org)", "scope_required": "Пожалуйста, введите область npm", "search": "Поиск", "search_error": "Ошибка поиска", "usage": "Использование", "version": "Версия"}, "errors": {"32000": "MCP сервер не запущен, пожалуйста, проверьте параметры"}, "serverPlural": "серверы", "serverSingular": "сервер", "title": "Серверы MCP", "startError": "Запуск не удалось", "type": "Тип", "updateError": "Ошибка обновления сервера", "updateSuccess": "Сервер успешно обновлен", "url": "URL", "editMcpJson": "Редактировать MCP", "installHelp": "Получить помощь по установке", "tabs": {"general": "Общие", "description": "Описание", "tools": "Инструменты", "prompts": "Подсказки", "resources": "Ресурсы"}, "tools": {"inputSchema": "Схема ввода", "availableTools": "Доступные инструменты", "noToolsAvailable": "Нет доступных инструментов", "loadError": "Ошибка получения инструментов"}, "prompts": {"availablePrompts": "Доступные подсказки", "noPromptsAvailable": "Нет доступных подсказок", "arguments": "Аргументы", "requiredField": "Обязательное поле", "genericError": "Ошибка получения подсказки", "loadError": "Ошибка получения подсказок"}, "resources": {"noResourcesAvailable": "Нет доступных ресурсов", "availableResources": "Доступные ресурсы", "uri": "URI", "mimeType": "MIME-тип", "size": "Размер", "blob": "Двоичные данные", "blobInvisible": "Скрытые двоичные данные", "text": "Текст"}, "deleteServer": "Удалить сервер", "deleteServerConfirm": "Вы уверены, что хотите удалить этот сервер?", "registry": "Реестр пакетов", "registryTooltip": "Выберите реестр для установки пакетов, если возникают проблемы с сетью при использовании реестра по умолчанию.", "registryDefault": "По умолчанию", "not_support": "Модель не поддерживается", "user": "Пользователь", "system": "Система", "types": {"inMemory": "Встроенный", "sse": "SSE", "streamableHttp": "Потоковый HTTP", "stdio": "STDIO"}, "sync": {"title": "Синхронизация серверов", "selectProvider": "Выберите провайдера:", "discoverMcpServers": "Обнаружить серверы MCP", "discoverMcpServersDescription": "Посетите платформу, чтобы обнаружить доступные серверы MCP", "getToken": "Получить API токен", "getTokenDescription": "Получите персональный API токен из вашей учетной записи", "setToken": "Введите ваш токен", "tokenRequired": "Требуется API токен", "tokenPlaceholder": "Введите API токен здесь", "button": "Синхронизировать", "error": "Ошибка синхронизации серверов MCP", "success": "Синхронизация серверов MCP успешна", "unauthorized": "Синхронизация не разрешена", "noServersAvailable": "Нет доступных серверов MCP"}, "timeout": "Тайм-аут", "timeoutTooltip": "Тайм-аут в секундах для запросов к этому серверу, по умолчанию 60 секунд", "provider": "Провайдер", "providerUrl": "URL провайдера", "logoUrl": "URL логотипа", "tags": "Теги", "tagsPlaceholder": "Введите теги", "providerPlaceholder": "Имя провайдера", "advancedSettings": "Расширенные настройки"}, "messages.prompt": "Показывать подсказки", "messages.tokens": "Показать использование токенов", "messages.divider": "Показывать разделитель между сообщениями", "messages.divider.tooltip": "Не применимо к сообщениям в стиле пузырей", "messages.grid_columns": "Количество столбцов сетки сообщений", "messages.grid_popover_trigger": "Триггер для отображения подробной информации в сетке", "messages.grid_popover_trigger.click": "Нажатие для отображения", "messages.grid_popover_trigger.hover": "Наведение для отображения", "messages.input.paste_long_text_as_file": "Вставлять длинный текст как файл", "messages.input.paste_long_text_threshold": "<PERSON><PERSON><PERSON><PERSON> вставки длинного текста", "messages.input.send_shortcuts": "Горячие клавиши для отправки", "messages.input.show_estimated_tokens": "Показывать затраты токенов", "messages.input.title": "Настройки ввода", "messages.input.enable_quick_triggers": "Включите / и @, чтобы вызвать быстрое меню.", "messages.input.enable_delete_model": "Включите удаление модели/вложения с помощью клавиши Backspace", "messages.markdown_rendering_input_message": "Отображение ввода в формате Markdown", "messages.math_engine": "Математический движок", "messages.math_engine.none": "Нет", "messages.metrics": "{{time_first_token_millsec}}ms до первого токена | {{token_speed}} tok/sec", "messages.model.title": "Настройки модели", "messages.navigation": "Навигация сообщений", "messages.navigation.anchor": "Ди<PERSON><PERSON><PERSON><PERSON> анкор", "messages.navigation.buttons": "Кнопки пагинации", "messages.navigation.none": "Не показывать", "messages.title": "Настройки сообщений", "messages.use_serif_font": "Использовать serif шрифт", "model": "Модель по умолчанию", "models.add.add_model": "Добавить модель", "models.add.group_name": "Имя группы", "models.add.group_name.placeholder": "Необязательно, например, ChatGPT", "models.add.group_name.tooltip": "Необязательно, например, ChatGPT", "models.add.model_id": "ID модели", "models.add.model_id.placeholder": "Обязательно, например, gpt-3.5-turbo", "models.add.model_id.select.placeholder": "Выберите модель", "models.add.model_id.tooltip": "Пример: gpt-3.5-turbo", "models.add.model_name": "Имя модели", "models.add.model_name.tooltip": "Необязательно, например, GPT-4", "models.add.model_name.placeholder": "Необязательно, например, GPT-4", "models.check.all": "Все", "models.check.all_models_passed": "Все модели прошли проверку", "models.check.button_caption": "Проверка состояния", "models.check.disabled": "Отключено", "models.check.enable_concurrent": "Параллельная проверка", "models.check.enabled": "Включено", "models.check.failed": "Не прошло", "models.check.keys_status_count": "Прошло: {{count_passed}} ключей, Не прошло: {{count_failed}} ключей", "models.check.model_status_failed": "{{count}} моделей полностью недоступны", "models.check.model_status_partial": "{{count}} моделей недоступны с некоторыми ключами", "models.check.model_status_passed": "{{count}} моделей прошли проверку состояния", "models.check.model_status_summary": "{{provider}}: {{summary}}", "models.check.no_api_keys": "API ключи не найдены, пожалуйста, добавьте API ключи.", "models.check.passed": "Прошло", "models.check.select_api_key": "Выберите API ключ для использования:", "models.check.single": "<PERSON><PERSON><PERSON><PERSON>", "models.check.start": "Начать", "models.check.title": "Проверка состояния моделей", "models.check.use_all_keys": "Использовать все ключи", "models.check.disclaimer": "Проверка состояния моделей требует отправки запросов, пожалуйста, используйте эту функцию с осторожностью. Модели, которые взимают плату за запросы, могут привести к дополнительным расходам, пожалуйста, самостоятельно несем ответственность за них.", "models.default_assistant_model": "Модель ассистента по умолчанию", "models.default_assistant_model_description": "Модель, используемая при создании нового ассистента, если ассистент не имеет настроенной модели, будет использоваться эта модель", "models.empty": "Модели не найдены", "models.enable_topic_naming": "Автоматическое переименование топика", "models.manage.add_listed": "Добавить в список", "models.manage.remove_listed": "Удалить из списка", "models.manage.add_whole_group": "Добавить всю группу", "models.manage.remove_whole_group": "Удалить всю группу", "models.topic_naming_model": "Модель именования топика", "models.topic_naming_model_description": "Модель, используемая при автоматическом именовании нового топика", "models.topic_naming_model_setting_title": "Настройки модели именования топика", "models.topic_naming_prompt": "Подсказка для именования топика", "models.translate_model": "Модель перевода", "models.translate_model_description": "Модель, используемая для сервиса перевода", "models.translate_model_prompt_message": "Введите модель перевода", "models.translate_model_prompt_title": "Модель перевода", "models.quick_assistant_model": "Модель быстрого помощника", "models.quick_assistant_model_description": "Модель по умолчанию, используемая быстрым помощником", "moresetting": "Дополнительные настройки", "moresetting.check.confirm": "Подтвердить выбор", "moresetting.check.warn": "Пожалуйста, будьте осторожны при выборе этой опции. Неправильный выбор может привести к сбою в работе модели!", "moresetting.warn": "Предупреждение о риске", "provider": {"add.name": "Имя провайдера", "add.name.placeholder": "Пример: OpenAI", "add.title": "Добавить провайдер", "add.type": "Тип провайдера", "api.url.preview": "Предпросмотр: {{url}}", "api.url.reset": "Сброс", "api.url.tip": "Заканчивая на / игнорирует v1, заканчивая на # принудительно использует введенный адрес", "api_host": "Хост API", "api_key": "Ключ API", "api_key.tip": "Несколько ключей, разделенных запятыми", "api_version": "Версия API", "basic_auth": "HTTP аутентификация", "basic_auth.tip": "Применимо к экземплярам, развернутым через сервер (см. документацию). В настоящее время поддерживается только схема Basic (RFC7617).", "basic_auth.user_name": "Имя пользователя", "basic_auth.user_name.tip": "Оставить пустым для отключения", "basic_auth.password": "Пароль", "basic_auth.password.tip": "", "charge": "Пополнить баланс", "bills": "Счета за услуги", "check": "Проверить", "check_all_keys": "Проверить все ключи", "check_multiple_keys": "Проверить несколько ключей API", "oauth": {"button": "Войти с {{provider}}", "description": "Сервис предоставляется <website>{{provider}}</website>", "official_website": "Официал<PERSON>ный сайт"}, "copilot": {"auth_failed": "Github Copilot认证失败", "auth_success": "Github Copilot认证成功", "auth_success_title": "Аутентификация успешна", "code_failed": "Получение кода устройства не удалось, пожалуйста, попробуйте еще раз.", "code_generated_desc": "Пожалуйста, скопируйте код устройства в приведенную ниже ссылку браузера.", "code_generated_title": "Получить код устройства", "confirm_login": "Чрезмерное использование может привести к блокировке вашего Github, будьте осторожны!!!!", "confirm_title": "Предупреждение о рисках", "connect": "Подк<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "custom_headers": "Пользовательские заголовки запроса", "description": "Ваша учетная запись Github должна подписаться на Copilot.", "expand": "развернуть", "headers_description": "Пользовательские заголовки запроса (формат json)", "invalid_json": "Ошибка формата JSON", "login": "Войти в Github", "logout": "Выйти и<PERSON>", "logout_failed": "Не удалось выйти, пожалуйста, повторите попытку.", "logout_success": "Успешно вышел", "model_setting": "Настройки модели", "open_verification_first": "Пожалуйста, сначала щелкните по ссылке выше, чтобы перейти на страницу проверки.", "rate_limit": "Ограничение скорости", "tooltip": "Для использования Github Copilot необходимо сначала войти в Github."}, "dmxapi": {"select_platform": "Выберите платформу"}, "delete.content": "Вы уверены, что хотите удалить этот провайдер?", "delete.title": "Удалить провайдер", "docs_check": "Проверить", "docs_more_details": "для получения дополнительной информации", "get_api_key": "Получить ключ API", "is_not_support_array_content": "Включить совместимый режим", "no_models_for_check": "Нет моделей для проверки (например, диалоговые модели)", "not_checked": "Не проверено", "remove_duplicate_keys": "Удалить дубликаты ключей", "remove_invalid_keys": "Удалить недействительные ключи", "search": "Поиск поставщиков...", "search_placeholder": "Поиск по ID или имени модели", "title": "Провайдеры моделей", "notes": {"title": "Заметки модели", "placeholder": "Введите содержимое в формате Markdown...", "markdown_editor_default_value": "Область предварительного просмотра"}, "openai": {"alert": "Поставщик OpenAI больше не поддерживает старые методы вызова. Если вы используете сторонний API, создайте нового поставщика услуг."}}, "proxy": {"mode": {"custom": "Пользовательский прокси", "none": "Не использовать прокси", "system": "Системный прокси", "title": "Режим прокси"}, "title": "Настройки прокси"}, "proxy.title": "Адрес прокси", "quickAssistant": {"click_tray_to_show": "Нажмите на иконку трея для запуска", "enable_quick_assistant": "Включить быстрый помощник", "read_clipboard_at_startup": "Чтение буфера обмена при запуске", "title": "Быстрый помощник", "use_shortcut_to_show": "Нажмите на иконку трея или используйте горячие клавиши для запуска"}, "shortcuts": {"action": "Действие", "clear_shortcut": "Очистить сочетание клавиш", "clear_topic": "Очистить все сообщения", "copy_last_message": "Копировать последнее сообщение", "exit_fullscreen": "Выйти из полноэкранного режима", "key": "Клавиша", "mini_window": "Быстрый помощник", "selection_assistant_toggle": "Переключить помощник выделения", "selection_assistant_select_text": "Помощник выделения: выделить текст", "new_topic": "Новый топик", "press_shortcut": "Нажмите сочетание клавиш", "reset_defaults": "Сбросить настройки по умолчанию", "reset_defaults_confirm": "Вы уверены, что хотите сбросить все горячие клавиши?", "reset_to_default": "Сбросить настройки по умолчанию", "search_message": "Поиск сообщения", "search_message_in_chat": "Поиск сообщения в текущем диалоге", "show_app": "Показать/скрыть приложение", "show_settings": "Открыть настройки", "title": "Горячие клавиши", "toggle_new_context": "Очистить контекст", "toggle_show_assistants": "Переключить отображение ассистентов", "toggle_show_topics": "Переключить отображение топиков", "zoom_in": "Увеличить", "zoom_out": "Уменьшить", "zoom_reset": "Сбросить масштаб"}, "theme.system": "Системная", "theme.dark": "Темная", "theme.light": "Светлая", "theme.title": "Тема", "theme.color_primary": "Цвет темы", "theme.window.style.opaque": "Непрозрачное окно", "theme.window.style.title": "Стиль окна", "theme.window.style.transparent": "Прозрачное окно", "title": "Настройки", "topic.position": "Позиция топиков", "topic.position.left": "Слева", "topic.position.right": "Справа", "topic.show.time": "Показывать время топика", "topic.pin_to_top": "Закрепленные топики сверху", "tray.onclose": "Свернуть в трей при закрытии", "tray.show": "Показать значок в трее", "tray.title": "Трей", "websearch": {"blacklist": "Черный список", "blacklist_description": "Результаты из следующих веб-сайтов не будут отображаться в результатах поиска", "check": "проверка", "check_failed": "Проверка не прошла", "check_success": "Проверка успешна", "get_api_key": "Получить ключ API", "no_provider_selected": "Пожалуйста, выберите поставщика поисковых услуг, затем проверьте.", "search_max_result": "Количество результатов поиска", "search_provider": "поиск сервисного провайдера", "search_provider_placeholder": "Выберите поставщика поисковых услуг", "search_result_default": "По умолчанию", "search_with_time": "По<PERSON><PERSON><PERSON>, содержащий дату", "tavily": {"api_key": "Ключ <PERSON> Tavily", "api_key.placeholder": "Введите ключ <PERSON>ly", "description": "Tavily — это поисковая система, специально разработанная для ИИ-агентов, предоставляющая актуальные результаты, умные предложения по запросам и глубокие исследовательские возможности", "title": "<PERSON><PERSON>"}, "title": "Поиск в Интернете", "blacklist_tooltip": "Шаблон: *://*.example.com/*\nРегулярное выражение: /example\\.(net|org)/", "subscribe": "Подписка на черный список", "subscribe_update": "Обновить", "subscribe_add": "Добавить", "subscribe_url": "URL подписки", "subscribe_name": "Альтернативное имя", "subscribe_name.placeholder": "Альтернативное имя, если в подписке нет названия.", "subscribe_add_success": "Подписка успешно добавлена!", "subscribe_delete": "Удалить", "overwrite": "Переопределить провайдера поиска", "overwrite_tooltip": "Использовать провайдера поиска вместо LLM", "apikey": "<PERSON> ключ", "free": "Бесплатно", "content_limit": "Ограничение длины текста", "content_limit_tooltip": "Ограничьте длину содержимого результатов поиска, контент, превышающий ограничение, будет обрезан."}, "general.auto_check_update.title": "Автоматическое обновление", "general.early_access.title": "Ранний доступ", "general.early_access.tooltip": "Включить для использования последней версии из GitHub, что может быть медленнее и нестабильно. Пожалуйста, сделайте резервную копию данных заранее.", "quickPhrase": {"title": "Быстрые фразы", "add": "Добавить фразу", "edit": "Редактировать фразу", "titleLabel": "Заголовок", "contentLabel": "Содержание", "titlePlaceholder": "Введите заголовок фразы", "contentPlaceholder": "Введите содержание фразы, поддерживает использование переменных, и нажмите Tab для быстрого перехода к переменной для изменения. Например: \nПомоги мне спланировать маршрут от ${from} до ${to} и отправить его на ${email}.", "delete": "Удалить фразу", "deleteConfirm": "После удаления фраза не может быть восстановлена, продолжить?", "locationLabel": "Место добавления", "global": "Глобальные быстрые фразы", "assistant": "Подсказки ассистента"}, "quickPanel": {"title": "Быстрое меню", "close": "Закрыть", "select": "Выбрать", "page": "Страница", "confirm": "Подтвердить", "back": "Назад", "forward": "Вперед", "multiple": "Множественный выбор"}, "privacy": {"title": "Настройки конфиденциальности", "enable_privacy_mode": "Анонимная отчетность об ошибках и статистике"}, "zoom": {"title": "Ма<PERSON><PERSON>т<PERSON><PERSON> страницы", "reset": "Сбросить"}, "input.show_translate_confirm": "Показать диалоговое окно подтверждения перевода", "openai": {"title": "Настройки OpenAI", "summary_text_mode.title": "Режим резюме", "summary_text_mode.tip": "Резюме рассуждений, выполненных моделью", "summary_text_mode.auto": "Авто", "summary_text_mode.concise": "Краткий", "summary_text_mode.detailed": "Подробный", "summary_text_mode.off": "Выключен", "service_tier.title": "Уровень сервиса", "service_tier.tip": "Указывает уровень задержки, который следует использовать для обработки запроса", "service_tier.auto": "Авто", "service_tier.default": "По умолчанию", "service_tier.flex": "Гибкий"}, "about.debug.title": "Отладка", "about.debug.open": "Открыть", "notification": {"title": "Настройки уведомлений", "assistant": "Сообщение ассистента", "backup": "Резервное сообщение", "knowledge_embed": "Сообщение базы знаний"}}, "translate": {"any.language": "Любой язык", "target_language": "Целевой язык", "alter_language": "Альтернативный язык", "button.translate": "Перевести", "close": "Закрыть", "closed": "Перевод закрыт", "copied": "Содержимое перевода скопировано", "empty": "Содержимое перевода пусто", "not.found": "Содержимое перевода не найдено", "confirm": {"content": "Перевод заменит исходный текст, продолжить?", "title": "Перевод подтверждение"}, "error.failed": "Перевод не удалось", "error.not_configured": "Модель перевода не настроена", "history": {"clear": "Очистить историю", "clear_description": "Очистка истории удалит все записи переводов. Продолжить?", "delete": "Удалить", "empty": "История переводов отсутствует", "title": "История переводов"}, "input.placeholder": "Введите текст для перевода", "output.placeholder": "Перевод", "processing": "Перевод в процессе...", "language.same": "Исходный и целевой языки совпадают", "language.not_pair": "Исходный язык отличается от настроенного", "settings": {"title": "Настройки перевода", "model": "Настройки модели", "model_desc": "Модель, используемая для службы перевода", "bidirectional": "Настройки двунаправленного перевода", "scroll_sync": "Настройки синхронизации прокрутки", "bidirectional_tip": "Если включено, перевод будет выполняться в обоих направлениях, исходный текст будет переведен на целевой язык и наоборот."}, "title": "Перевод", "tooltip.newline": "Перевести", "menu": {"description": "Перевести содержимое текущего ввода"}, "detected.language": "Автоматическое обнаружение"}, "tray": {"quit": "Выйти", "show_mini_window": "Быстрый помощник", "show_window": "Показать окно"}, "words": {"knowledgeGraph": "<PERSON><PERSON><PERSON><PERSON>", "quit": "Выйти", "show_window": "Показать окно", "visualization": "Визуализация"}, "update": {"title": "Обновление", "message": "Новая версия {{version}} готова, установить сейчас?", "later": "Позже", "install": "Установить", "noReleaseNotes": "Нет заметок об обновлении"}, "selection": {"name": "Помощник выбора", "action": {"builtin": {"translate": "Перевести", "explain": "Объяснить", "summary": "Суммаризировать", "search": "Поиск", "refine": "Уточнить", "copy": "Копировать", "quote": "Цитировать"}, "window": {"pin": "Закрепить", "pinned": "Закреплено", "opacity": "Прозрачность окна", "original_show": "Показать оригинал", "original_hide": "Скрыть оригинал", "original_copy": "Копировать оригинал", "esc_close": "Esc - закрыть", "esc_stop": "Esc - остановить", "c_copy": "C - копировать", "r_regenerate": "R - перегенерировать"}, "translate": {"smart_translate_tips": "Смарт-перевод: содержимое будет переведено на целевой язык; содержимое уже на целевом языке будет переведено на альтернативный язык"}}, "settings": {"experimental": "Экспериментальные функции", "enable": {"title": "Включить", "description": "Поддерживается только в Windows"}, "toolbar": {"title": "Панель инструментов", "trigger_mode": {"title": "Режим активации", "description": "Показывать панель сразу при выделении, или только при удержании Ctrl, или только при нажатии на сочетание клавиш", "description_note": "В некоторых приложениях Ctrl может не работать. Если вы используете AHK или другие инструменты для переназначения Ctrl, это может привести к тому, что некоторые приложения не смогут выделить текст.", "selected": "При выделении", "selected_note": "После выделения", "ctrlkey": "По Ctrl", "ctrlkey_note": "После выделения, удерживайте Ctrl для показа панели. Пожалуйста, установите Ctrl в настройках клавиатуры и активируйте его.", "shortcut": "По сочетанию клавиш", "shortcut_note": "После выделения, используйте сочетание клавиш для показа панели. Пожалуйста, установите сочетание клавиш в настройках клавиатуры и активируйте его.", "shortcut_link": "Перейти к настройкам клавиатуры"}, "compact_mode": {"title": "Компактный режим", "description": "Отображать только иконки без текста"}}, "window": {"title": "Окно действий", "follow_toolbar": {"title": "Следовать за панелью", "description": "Окно будет следовать за панелью. Иначе - по центру."}, "remember_size": {"title": "Запомнить размер", "description": "При отключенном режиме, окно будет восстанавливаться до последнего размера при запуске приложения"}, "auto_close": {"title": "Автозакрытие", "description": "Закрывать окно при потере фокуса (если не закреплено)"}, "auto_pin": {"title": "Автозакрепление", "description": "Закреплять окно по умолчанию"}, "opacity": {"title": "Прозрачность", "description": "Установить прозрачность окна по умолчанию"}}, "actions": {"title": "Действия", "custom": "Пользовательское действие", "reset": {"button": "Сбросить", "tooltip": "Сбросить стандартные действия. Пользовательские останутся.", "confirm": "Сбросить стандартные действия? Пользовательские останутся."}, "add_tooltip": {"enabled": "Добавить действие", "disabled": "Достигнут лимит ({{max}})"}, "delete_confirm": "Удалить это действие?", "drag_hint": "Перетащите для сортировки. Включено: {{enabled}}/{{max}}"}, "advanced": {"title": "Расширенные", "filter_mode": {"title": "Режим фильтрации", "description": "Можно ограничить выборку по определенным приложениям (белый список) или исключить их (черный список)", "default": "Выключено", "whitelist": "Белый список", "blacklist": "Черный список"}, "filter_list": {"title": "Список фильтрации", "description": "Расширенная функция, рекомендуется для пользователей с опытом"}}, "user_modal": {"title": {"add": "Добавить действие", "edit": "Редактировать действие"}, "name": {"label": "Название", "hint": "Введите название"}, "icon": {"label": "Иконка", "placeholder": "Название иконки Lucide", "error": "Некорректное название", "tooltip": "Названия в lowercase, например arrow-right", "view_all": "Все иконки", "random": "Случайная"}, "model": {"label": "Модель", "tooltip": "Использовать ассистента: будут применены его системные настройки", "default": "По умолчанию", "assistant": "Ассистент"}, "assistant": {"label": "Ассистент", "default": "По умолчанию"}, "prompt": {"label": "Промпт", "tooltip": "Дополняет ввод пользователя, не заменяя системный промпт ассистента", "placeholder": "Используйте {{text}} для выделенного текста. Если пусто - текст будет добавлен", "placeholder_text": "Плейсхолдер", "copy_placeholder": "Копировать плейсхолдер"}}, "search_modal": {"title": "Поисковая система", "engine": {"label": "Поисковик", "custom": "Свой"}, "custom": {"name": {"label": "Название", "hint": "Название поисковика", "max_length": "Не более 16 символов"}, "url": {"label": "URL поиска", "hint": "Используйте {{queryString}} для представления поискового запроса", "required": "Введите URL", "invalid_format": "URL должен начинаться с http:// или https://", "missing_placeholder": "Должен содержать {{queryString}}"}, "test": "Тест"}}, "filter_modal": {"title": "Список фильтрации", "user_tips": "Введите имя исполняемого файла приложения, один на строку, не учитывая регистр, можно использовать подстановку *"}}}}}